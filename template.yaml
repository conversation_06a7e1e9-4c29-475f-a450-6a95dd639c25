AWSTemplateFormatVersion: '2010-09-09'
Transform: AWS::Serverless-2016-10-31

Globals:
  Function:
    Timeout: 300
    MemorySize: 1024
    Runtime: python3.11
    Architectures:
      - x86_64
    Environment:
      Variables:
        DB_HOST: !Ref DatabaseHost
        DB_PORT: !Ref DatabasePort
        DB_NAME: !Ref DatabaseName
        DB_USERNAME: !Ref DatabaseUsername
        DB_PASSWORD: !Ref DatabasePassword
        DB_SCHEMA: !Ref DatabaseSchema
        SMS_INPUT_QUEUE_URL: !Ref SMSInputQueue
        SMS_BATCH_QUEUE_URL: !Ref SMSBatchQueue
        COMPLETION_QUEUE_URL: !Ref CompletionQueue
        BATCH_SIZE: !Ref BatchSize
        OPENAI_API_KEY: !Ref OpenAIApiKey
        EXTERNAL_API_ENDPOINT: !Ref ExternalApiEndpoint
        ENVIRONMENT: !Ref Environment
    Layers:
      - !Ref SharedLayer

Parameters:
  Environment:
    Type: String
    Default: dev
    AllowedValues:
      - dev
      - staging
      - prod
    Description: Environment name

  DatabaseHost:
    Type: String
    Default: modus-db-dev.cvui26wus1f1.ap-south-1.rds.amazonaws.com
    Description: PostgreSQL database host/endpoint
    
  DatabasePort:
    Type: String
    Default: "5432"
    Description: PostgreSQL database port

  DatabaseUsername:
    Type: String
    Default: postgres
    NoEcho: true
    Description: PostgreSQL username

  DatabasePassword:
    Type: String
    NoEcho: true
    MinLength: 8
    Description: PostgreSQL password

  DatabaseName:
    Type: String
    Default: "postgres"
    Description: PostgreSQL database name

  DatabaseSchema:
    Type: String
    Default: "customer_investigation_kb"
    Description: PostgreSQL schema name

  BatchSize:
    Type: Number
    Default: 50
    MinValue: 1
    MaxValue: 100
    Description: Number of messages to process in each batch

  OpenAIApiKey:
    Type: String
    NoEcho: true
    Description: OpenAI API key for message analysis

  ExternalApiEndpoint:
    Type: String
    Description: External API endpoint to signal completion

Resources:
  # Shared Layer for common utilities
  SharedLayer:
    Type: AWS::Serverless::LayerVersion
    Properties:
      LayerName: !Sub "sms-processing-shared-${Environment}"
      Description: Shared utilities for SMS processing
      ContentUri: layers/shared/
      CompatibleRuntimes:
        - python3.11
      RetentionPolicy: Delete

  # SQS Queues
  SMSInputQueue:
    Type: AWS::SQS::Queue
    Properties:
      QueueName: !Sub "sms-input-queue-${Environment}"
      VisibilityTimeout: 900
      MessageRetentionPeriod: 1209600
      RedrivePolicy:
        deadLetterTargetArn: !GetAtt SMSInputDLQ.Arn
        maxReceiveCount: 3

  SMSInputDLQ:
    Type: AWS::SQS::Queue
    Properties:
      QueueName: !Sub "sms-input-dlq-${Environment}"
      MessageRetentionPeriod: 1209600

  SMSBatchQueue:
    Type: AWS::SQS::Queue
    Properties:
      QueueName: !Sub "sms-batch-queue-${Environment}"
      VisibilityTimeout: 900
      MessageRetentionPeriod: 1209600
      RedrivePolicy:
        deadLetterTargetArn: !GetAtt SMSBatchDLQ.Arn
        maxReceiveCount: 3

  SMSBatchDLQ:
    Type: AWS::SQS::Queue
    Properties:
      QueueName: !Sub "sms-batch-dlq-${Environment}"
      MessageRetentionPeriod: 1209600

  CompletionQueue:
    Type: AWS::SQS::Queue
    Properties:
      QueueName: !Sub "completion-queue-${Environment}"
      VisibilityTimeout: 300
      MessageRetentionPeriod: 1209600
      RedrivePolicy:
        deadLetterTargetArn: !GetAtt CompletionDLQ.Arn
        maxReceiveCount: 3

  CompletionDLQ:
    Type: AWS::SQS::Queue
    Properties:
      QueueName: !Sub "completion-dlq-${Environment}"
      MessageRetentionPeriod: 1209600

  # Lambda Functions
  SMSSegregationFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: lambdas/sms-segregation/
      Handler: lambda_function.lambda_handler
      Description: Segregates SMS messages into financial and non-financial
      Events:
        SQSEvent:
          Type: SQS
          Properties:
            Queue: !GetAtt SMSInputQueue.Arn
            BatchSize: 1
      Policies:
        - SQSPollerPolicy:
            QueueName: !GetAtt SMSInputQueue.QueueName
        - SQSSendMessagePolicy:
            QueueName: !GetAtt SMSBatchQueue.QueueName
        - S3ReadPolicy:
            BucketName: "*"
        - Statement:
            - Effect: Allow
              Action:
                - rds:DescribeDBInstances
                - rds-db:connect
              Resource: "*"

  MessageAnalysisFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: lambdas/message-analysis/
      Handler: lambda_function.lambda_handler
      Description: Analyzes SMS messages using OpenAI API
      Timeout: 900
      Events:
        SQSEvent:
          Type: SQS
          Properties:
            Queue: !GetAtt SMSBatchQueue.Arn
            BatchSize: 1
      Policies:
        - SQSPollerPolicy:
            QueueName: !GetAtt SMSBatchQueue.QueueName
        - SQSSendMessagePolicy:
            QueueName: !GetAtt CompletionQueue.QueueName
        - Statement:
            - Effect: Allow
              Action:
                - rds:DescribeDBInstances
                - rds-db:connect
              Resource: "*"

  CompletionCheckFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: lambdas/completion-check/
      Handler: lambda_function.lambda_handler
      Description: Checks completion status and calls external API
      Events:
        SQSEvent:
          Type: SQS
          Properties:
            Queue: !GetAtt CompletionQueue.Arn
            BatchSize: 1
      Policies:
        - SQSPollerPolicy:
            QueueName: !GetAtt CompletionQueue.QueueName
        - Statement:
            - Effect: Allow
              Action:
                - rds:DescribeDBInstances
                - rds-db:connect
              Resource: "*"

Outputs:
  SMSInputQueueUrl:
    Description: "URL of the SMS Input Queue"
    Value: !Ref SMSInputQueue
    Export:
      Name: !Sub "${AWS::StackName}-SMSInputQueueUrl"

  SMSBatchQueueUrl:
    Description: "URL of the SMS Batch Queue"
    Value: !Ref SMSBatchQueue
    Export:
      Name: !Sub "${AWS::StackName}-SMSBatchQueueUrl"

  CompletionQueueUrl:
    Description: "URL of the Completion Queue"
    Value: !Ref CompletionQueue
    Export:
      Name: !Sub "${AWS::StackName}-CompletionQueueUrl"

  SharedLayerArn:
    Description: "ARN of the shared layer"
    Value: !Ref SharedLayer
    Export:
      Name: !Sub "${AWS::StackName}-SharedLayerArn"
