# SMS Processing Pipeline

A serverless data processing pipeline on AWS for analyzing SMS messages using SAM (Serverless Application Model), Python, and PostgreSQL.

## Architecture

```
External Service → SQS Input Queue → Lambda 1 (SMS Segregation) → SQS Batch Queue → Lambda 2 (Message Analysis) → SQS Completion Queue → Lambda 3 (Completion Check) → External API
```

## Components

### Lambda Functions
- **`lambdas/sms-segregation/`** - Reads S3 files, classifies messages as financial/non-financial, creates batches
- **`lambdas/message-analysis/`** - Analyzes messages using OpenAI API in parallel
- **`lambdas/completion-check/`** - Checks completion status and calls external API

### Shared Layer
- **`layers/shared/python/`** - Common utilities for database operations, SMS classification, and API calls

### Infrastructure
- **`template.yaml`** - SAM template defining all AWS resources (SQS, Lambda, IAM)
- **`schema.sql`** - PostgreSQL database schema for `customer_investigation_kb`
- **`samconfig.toml`** - SAM deployment configuration

## Quick Start

### 1. Prerequisites
```bash
# Install AWS SAM CLI
pip install aws-sam-cli

# Install dependencies
pip install -r pipeline-requirements.txt

# Configure AWS CLI
aws configure
```

### 2. Environment Setup
```bash
# Create environment file
cp .env.example .env
# Edit .env with your database credentials, OpenAI API key, external API endpoint
```

### 3. Deploy
```bash
# Option 1: Guided deployment (recommended for first time)
./deploy.sh --guided --init-db

# Option 2: Automated deployment
make deploy
```

### 4. Test
```bash
python test_pipeline.py
```

## Configuration

### Environment Variables
Update `samconfig.toml` with your values:
- **DatabaseHost**: Your PostgreSQL RDS endpoint
- **DatabasePassword**: Database password
- **OpenAIApiKey**: Your OpenAI API key
- **ExternalApiEndpoint**: Webhook URL for completion notifications
- **BatchSize**: Number of messages per batch (default: 50)

## Usage

### Trigger Processing
Send a message to the SMS Input Queue:
```json
{
  "s3_path": "s3://your-bucket/path/to/sms-file.csv",
  "customer_id": "customer_123"
}
```

### Supported File Formats
- **CSV**: Columns named `message`, `text`, or `body`
- **JSON**: Array with `message` or `text` fields
- **TXT**: Each line is a separate message

## Monitoring

### CloudWatch Logs
- `/aws/lambda/sms-processing-pipeline-SMSSegregationFunction-*`
- `/aws/lambda/sms-processing-pipeline-MessageAnalysisFunction-*`
- `/aws/lambda/sms-processing-pipeline-CompletionCheckFunction-*`

### Database Monitoring
```sql
-- Check processing status
SELECT * FROM customer_investigation_kb.customer_processing_status;

-- Check recent logs
SELECT * FROM customer_investigation_kb.processing_logs 
ORDER BY created_at DESC LIMIT 10;
```

## Key Features

- **Scalable**: Serverless architecture with auto-scaling
- **Reliable**: Dead Letter Queues and comprehensive error handling
- **Secure**: IAM roles with least privilege access
- **Observable**: CloudWatch logs and database event tracking
- **Configurable**: Environment-based configuration
- **Async Processing**: Parallel OpenAI API calls for efficiency

## Database Schema

- `sms_messages` - Store SMS messages and analysis results
- `batch_processing` - Track batch processing status
- `customer_processing_status` - Customer-level processing tracking
- `processing_logs` - Comprehensive event logging

## Build Commands

```bash
make build          # Build all Lambda functions
make validate       # Validate SAM template
make deploy         # Deploy using SAM
make test           # Run tests
make clean          # Clean build artifacts
```

## Support

For detailed setup instructions, see `SMS_PIPELINE_COMPLETE_SETUP.md`.
