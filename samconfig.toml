version = 0.1

[default.deploy.parameters]
stack_name = "sms-processing-pipeline"
resolve_s3 = true
s3_prefix = "sms-processing-pipeline"
region = "ap-south-1"
confirm_changeset = true
capabilities = "CAPABILITY_IAM"
parameter_overrides = "Environment=\"dev\" DatabaseHost=\"modus-db-dev.cvui26wus1f1.ap-south-1.rds.amazonaws.com\" DatabasePort=\"5432\" DatabaseName=\"postgres\" DatabaseUsername=\"postgres\" DatabasePassword=\"your-db-password\" DatabaseSchema=\"customer_investigation_kb\" BatchSize=\"50\" OpenAIApiKey=\"your-openai-api-key\" ExternalApiEndpoint=\"https://your-external-api.com/webhook\""
image_repositories = []

[default.build.parameters]
cached = true
parallel = true

[default.validate.parameters]
lint = true

[default.local_start_api.parameters]
warm_containers = "EAGER"

[default.local_start_lambda.parameters]
warm_containers = "EAGER"
