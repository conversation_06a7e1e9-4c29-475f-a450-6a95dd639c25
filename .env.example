# SMS Processing Pipeline Environment Variables

# Database Configuration
DB_HOST=modus-db-dev.cvui26wus1f1.ap-south-1.rds.amazonaws.com
DB_PORT=5432
DB_NAME=postgres
DB_USERNAME=postgres
DB_PASSWORD=your-database-password
DB_SCHEMA=customer_investigation_kb

# Processing Configuration
BATCH_SIZE=50

# External API Configuration
OPENAI_API_KEY=your-openai-api-key
EXTERNAL_API_ENDPOINT=https://your-external-api.com/webhook

# AWS Configuration (for local development)
AWS_REGION=ap-south-1
AWS_PROFILE=default

# Environment
ENVIRONMENT=dev
