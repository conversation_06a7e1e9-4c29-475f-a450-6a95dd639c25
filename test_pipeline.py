#!/usr/bin/env python3
"""
Test script for SMS Processing Pipeline
"""
import json
import boto3
import csv
import io
import os
from datetime import datetime

def create_test_sms_file():
    """Create a test SMS file with sample messages matching the enhanced classifier"""
    test_messages = [
        # Financial - Debit transactions
        "Your account XXXX1234 has been debited by Rs 1,500.00 on 15-Jan-2024 at ATM. Ref: TXN123456789",
        "Card ending 5678 used for purchase of Rs 2,350.50 at AMAZON on 16-Jan-2024. Available bal: Rs 45,000",
        "UPI payment of Rs 750 to merchant@paytm successful. UPI ID: user@okaxis. Ref: ************",

        # Financial - Credit transactions
        "Your account XXXX9876 credited with Rs 50,000 on 17-Jan-2024. Salary credit from COMPANY LTD",
        "NEFT credit of Rs 5,000 received from JOHN DOE on 18-Jan-2024. Ref: N123456789012",
        "Cashback of Rs 125.50 credited to your account for transaction on 19-Jan-2024",

        # Financial - Balance inquiries
        "Account balance inquiry: Available balance Rs 67,500.25 as on 20-Jan-2024. Last txn: Rs 1,200 debit",
        "Your account XXXX4321 balance is Rs 1,25,000 as on 21-Jan-2024 10:30 AM",

        # Financial - OTP/Security
        "OTP for transaction of Rs 3,500 is 456789. Valid for 10 minutes. Do not share",
        "Security alert: Login attempt from new device on 22-Jan-2024. If not you, call 1800-xxx-xxxx",

        # Non-Financial messages
        "Happy birthday! Hope you have a wonderful day ahead. Best wishes from your bank",
        "Weather update: Heavy rain expected today. Carry umbrella and stay safe",
        "Congratulations! You have won a prize worth Rs 10,000 in our lucky draw contest",
        "Your order #ORD123456 has been delivered successfully. Rate your experience",
        "Special offer: Get 50% discount on all products. Limited time offer. Shop now!",
        "Reminder: Your insurance premium is due on 25-Jan-2024. Pay online to avoid penalty"
    ]
    
    # Create CSV content
    csv_content = io.StringIO()
    writer = csv.writer(csv_content)
    writer.writerow(['message', 'sender', 'timestamp'])
    
    for i, message in enumerate(test_messages):
        writer.writerow([message, f'sender_{i}', datetime.now().isoformat()])
    
    return csv_content.getvalue()

def upload_test_file_to_s3(bucket_name, file_key, content):
    """Upload test file to S3"""
    try:
        s3_client = boto3.client('s3')
        s3_client.put_object(
            Bucket=bucket_name,
            Key=file_key,
            Body=content,
            ContentType='text/csv'
        )
        s3_path = f"s3://{bucket_name}/{file_key}"
        print(f"✅ Test file uploaded to: {s3_path}")
        return s3_path
    except Exception as e:
        print(f"❌ Failed to upload test file: {str(e)}")
        return None

def send_test_message_to_sqs(queue_url, s3_path, customer_id):
    """Send test message to SQS Input Queue"""
    try:
        sqs_client = boto3.client('sqs')
        
        message_body = {
            "s3_path": s3_path,
            "customer_id": customer_id
        }
        
        response = sqs_client.send_message(
            QueueUrl=queue_url,
            MessageBody=json.dumps(message_body)
        )
        
        print(f"✅ Test message sent to SQS: {response['MessageId']}")
        return response['MessageId']
    except Exception as e:
        print(f"❌ Failed to send test message: {str(e)}")
        return None

def get_stack_outputs(stack_name):
    """Get CloudFormation stack outputs"""
    try:
        cf_client = boto3.client('cloudformation')
        response = cf_client.describe_stacks(StackName=stack_name)
        
        outputs = {}
        for output in response['Stacks'][0].get('Outputs', []):
            outputs[output['OutputKey']] = output['OutputValue']
        
        return outputs
    except Exception as e:
        print(f"❌ Failed to get stack outputs: {str(e)}")
        return {}

def check_database_connection():
    """Check if database connection is working"""
    try:
        import psycopg2
        
        # Get database connection details from environment
        db_config = {
            'host': os.environ.get('DB_HOST', 'modus-db-dev.cvui26wus1f1.ap-south-1.rds.amazonaws.com'),
            'port': os.environ.get('DB_PORT', '5432'),
            'database': os.environ.get('DB_NAME', 'postgres'),
            'user': os.environ.get('DB_USERNAME', 'postgres'),
            'password': os.environ.get('DB_PASSWORD', '')
        }
        
        if not db_config['password']:
            print("⚠️  Database password not set in environment variables")
            return False
        
        # Test connection
        conn = psycopg2.connect(**db_config)
        cursor = conn.cursor()
        cursor.execute("SELECT 1")
        cursor.close()
        conn.close()
        
        print("✅ Database connection successful")
        return True
    except Exception as e:
        print(f"❌ Database connection failed: {str(e)}")
        return False

def main():
    """Main test function"""
    print("🚀 SMS Processing Pipeline Test")
    print("=" * 50)
    
    # Configuration
    stack_name = "sms-processing-pipeline"
    bucket_name = input("Enter S3 bucket name for test files: ").strip()
    customer_id = f"test_customer_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    if not bucket_name:
        print("❌ S3 bucket name is required")
        return
    
    # Step 1: Check database connection
    print("\n1. Checking database connection...")
    if not check_database_connection():
        print("⚠️  Database connection failed. Please check your environment variables.")
    
    # Step 2: Get stack outputs
    print("\n2. Getting CloudFormation stack outputs...")
    outputs = get_stack_outputs(stack_name)
    
    if not outputs:
        print("❌ Failed to get stack outputs. Make sure the stack is deployed.")
        return
    
    sms_input_queue_url = outputs.get('SMSInputQueueUrl')
    if not sms_input_queue_url:
        print("❌ SMS Input Queue URL not found in stack outputs")
        return
    
    print(f"✅ Found SMS Input Queue: {sms_input_queue_url}")
    
    # Step 3: Create and upload test file
    print("\n3. Creating test SMS file...")
    test_content = create_test_sms_file()
    
    file_key = f"test_sms_files/{customer_id}/test_messages.csv"
    s3_path = upload_test_file_to_s3(bucket_name, file_key, test_content)
    
    if not s3_path:
        return
    
    # Step 4: Send test message to SQS
    print("\n4. Sending test message to SQS...")
    message_id = send_test_message_to_sqs(sms_input_queue_url, s3_path, customer_id)
    
    if not message_id:
        return
    
    # Step 5: Provide monitoring instructions
    print("\n5. Test message sent successfully! 🎉")
    print("\nMonitoring Instructions:")
    print("-" * 30)
    print(f"Customer ID: {customer_id}")
    print(f"S3 Path: {s3_path}")
    print(f"SQS Message ID: {message_id}")
    
    print("\nTo monitor progress:")
    print("1. Check CloudWatch Logs:")
    print("   - /aws/lambda/sms-processing-pipeline-SMSSegregationFunction-*")
    print("   - /aws/lambda/sms-processing-pipeline-MessageAnalysisFunction-*")
    print("   - /aws/lambda/sms-processing-pipeline-CompletionCheckFunction-*")
    
    print("\n2. Check database tables:")
    print(f"   SELECT * FROM customer_investigation_kb.customer_processing_status WHERE customer_id = '{customer_id}';")
    print(f"   SELECT * FROM customer_investigation_kb.sms_messages WHERE customer_id = '{customer_id}';")
    print(f"   SELECT * FROM customer_investigation_kb.processing_logs WHERE customer_id = '{customer_id}' ORDER BY created_at DESC;")
    
    print("\n3. Monitor SQS queues in AWS Console for message processing")
    
    print("\n✅ Test completed successfully!")

if __name__ == "__main__":
    main()
