import asyncio
import json
import csv
import os
from typing import List, Literal, Optional
import time

from langchain_openai import ChatOpenAI
from langchain.prompts import ChatPromptTemplate
from pydantic import BaseModel, Field

# Import existing classification logic
from classifiers import SMSClassifier
from field_extractors import FieldExtractor
from utils import clean_text

# --- Pydantic Models for Structured LLM Output ---

# First call: Classification only - now supports multiple classifications
class SingleClassification(BaseModel):
    """Represents a single classification of a financial message."""
    type: Literal[
        "Accounts",
        "Deposit & Withdrawal",
        "Investment",
        "Money Transfer",
        "Payment",
        "Purchase",
        "Non Financial",
    ] = Field(..., description="The main category of the message.")
    subtype: Optional[str] = Field(
        None, description="The specific sub-category within the main type."
    )
    infotype: Optional[Literal["Account Status", "Application", "Balance Update", "Inflow", "Outflow"]] = Field(
        None, description="The nature of the information in the message."
    )
    confidence: Literal["high", "medium", "low"] = Field(
        ..., description="Confidence level for this classification."
    )

class MessageClassification(BaseModel):
    """Represents multiple classifications of a financial message."""
    classifications: List[SingleClassification] = Field(
        ..., description="List of possible classifications for the message, only include high confidence classifications."
    )

# Second call: Information extraction (dynamic fields based on type)
class MessageExtraction(BaseModel):
    """Represents the information extraction from a financial message."""
    # This will be dynamically created based on the type from the mapping file

async def classify_message(llm: ChatOpenAI, message: dict) -> List[dict]:
    """
    First LLM call: Classify the message into multiple possible types, subtypes, and infotypes.

    Args:
        llm: The initialized ChatOpenAI model instance.
        message: A dictionary representing a single message.

    Returns:
        A list of dictionaries containing the classification results.
    """
    prompt = ChatPromptTemplate.from_messages(
        [
            (
                "system",
                """
                You are a message classification system. Your task is to analyze the content of a given message and classify it into one or more specific `type`, `subtype`, and `infotype` combinations.

                #### **► Crucial Classification Instructions**

                *   **Multiple Classifications**: A single message can have multiple classifications if it contains information about different financial activities. For example, a message about a purchase that also mentions cashback could be both "Purchase" and "Money Transfer".
                *   **Payment, deposit and withdrawal, investment, moneytransfer and purchase messages**: The message must have a clear amount or transaction value to be classified as one of these types.
                If the message doesn't have a clear amount or transaction value, classify into the other types.
                *   **High Confidence Only**: Only include classifications where you have HIGH confidence. When in doubt, exclude the classification.
                *   **Messages about due payments:** Messages about amount due for a bill, EMI, or loan should not be classified as `Payment` or `Deposit & Withdrawal` as the payment has not yet been made.
                *   **Offers, Promotions, and Advertisements are ALWAYS `Non Financial`**: Messages whose primary purpose is to market a product or service must be classified as `type: Non Financial`. This includes, but is not limited to:
                    *   **Loan Offers**: Pre-approved loan offers, invitations to apply for a loan, or any marketing for credit products.
                    *   **Discounts & Sales**: Promotional messages about discou1nts, "sale" events, or special offers on goods and services.
                    *   **Lotteries & Rewards**: Notifications about winning a lottery, cashback offers that are not yet credited, or reward point promotions.
                    *   *If the message is an offer or advertisement, classify it as `Non Financial` directly. For this type, `subtype` and `infotype` should be `null`.*
                    *   **Financial advices** like fraud prevention tips or security measure, or any other non-transactional financial advice should also be classified as `Non Financial`. Offers and advices like you can now pay your credit card bills using UPI, or you can now pay your loan EMIs using UPI should be classified as `Non Financial`.
                    *   **OTP Messages**: Messages that are primarily contains one time passwords, login codes, TPIN for account setups or password reset should be excluded.
                    *   **Messages mentioning password change and new login detected:** Messages that inform about new logins, password changes, password expiry should be assigned `Non Financial`.
                    *   **Messages mentioning discount, cashback, or offers**: If the message is about offering discounts on paying by a particular payment method, cashback on a purchase, or any other promotional offer, classify it as `Non Financial`.
                *   **`Accounts` vs. a Transaction**: `Accounts` type is for messages about the status or management of an account (e.g., application approval, KYC update). It is not for a single transaction (e.g., a purchase or transfer).
                *   **`Payment` vs. `Purchase`**: `Payment` is for recurring obligations like bills and EMIs. `Purchase` is for buying specific goods or services from a merchant.

                #### **► Classification Definitions Table**
                | type                 | subtype                                                                                                       | infotype                               |
                | -------------------- | ------------------------------------------------------------------------------------------------------------- | -------------------------------------- |
                | Accounts             | Bank Account, Brokerage Account, Credit Card, Credit Report, Fixed Deposit, Insurance, LIC/ULIP Scheme, Loan, PF Account, UPI, Wallet | Account Status, Application, Balance Update |
                | Deposit & Withdrawal | Annual Bonus, Cash Deposit, Cash Withdrawal, Check Deposit, Loan Disbursal, Monthly Salary Credit              | Inflow, Outflow                        |
                | Investment           | Crypto, FD, Gold, Insurance, LIC/ULIP Schemes, MF, PPF, Post-Office, Stocks                                   | Inflow, Outflow                        |
                | Money Transfer       | Brokerage Fund/Margin, Crypto Wallet, Money Wallet, IMPS, NEFT, RTGS, Remittance, UPI, Wallet Deposit          | Inflow, Outflow                        |
                | Payment              | Bill Payment, Credit Card, EMI Payment, Recharge Payment                                                      | Outflow                                |
                | Purchase             | Check, Credit Card, Debit Card, NEFT, RTGS, UPI                                                               | Inflow, Outflow                        |
                | Non Financial        | Promotions, Offers, Joining Discount, Loan Offer, Sales, Lotteries                                           | `null`                                     |

                **Important**: Only return classifications with HIGH confidence. If you're unsure about a classification, don't include it.

                Return a list of classifications, each with type, subtype, infotype, and confidence level.
                """,
            ),
            ("human", "Classify the following message (return only HIGH confidence classifications):\n\n---\n{message_body}\n---"),
        ]
    )

    chain = prompt | llm.with_structured_output(MessageClassification)

    try:
        message_body = message.get("body", "")
        if not message_body:
            return [{
                "classified_type": "Non Financial",
                "classified_subtype": None,
                "classified_infotype": None,
                "error_message": "Empty message body"
            }]
        
        result = await chain.ainvoke({"message_body": message_body})
        
        # Filter for high confidence classifications only
        high_confidence_classifications = [
            {
                "classified_type": classification.type,
                "classified_subtype": classification.subtype if classification.subtype != 'null' and classification.subtype is not None else None,
                "classified_infotype": classification.infotype if classification.infotype != 'null' and classification.infotype is not None else None,
                "error_message": None
            }
            for classification in result.classifications
            if classification.confidence == "high"
        ]
        
        # If no high confidence classifications, return Non Financial as fallback
        if not high_confidence_classifications:
            return [{
                "classified_type": "Non Financial",
                "classified_subtype": None,
                "classified_infotype": None,
                "error_message": "No high confidence classifications found"
            }]
            
        return high_confidence_classifications
        
    except Exception as e:
        return [{
            "classified_type": "Error",
            "classified_subtype": str(e),
            "classified_infotype": None,
            "error_message": "Failed to classify message"
        }]

def create_dynamic_extraction_model(type_name: str, field_mapping: dict):
    """
    Create a dynamic Pydantic model based on the type and field mapping.
    
    Args:
        type_name: The classified type of the message
        field_mapping: The field definitions for this type from the JSON file
    
    Returns:
        A dynamically created Pydantic model class
    """
    # Create annotations and field definitions separately
    annotations = {}
    field_definitions = {}
    
    for field_name, description in field_mapping.items():
        annotations[field_name] = Optional[str]
        field_definitions[field_name] = Field(None, description=description)
    
    # Create the class attributes dictionary
    class_attrs = {
        "__annotations__": annotations,
        **field_definitions
    }
    
    return type("DynamicMessageExtraction", (BaseModel,), class_attrs)

async def extract_information(llm: ChatOpenAI, message: dict, message_type: str, type_field_mapping: dict) -> dict:
    """
    Second LLM call: Extract specific information based on the classified type.

    Args:
        llm: The initialized ChatOpenAI model instance.
        message: A dictionary representing a single message.
        message_type: The classified type from the first call.
        type_field_mapping: The field mapping from the JSON file.

    Returns:
        A dictionary containing the extracted information.
    """
    if message_type == "Non Financial" or message_type == "Error":
        # For Non Financial and Error types, return minimal extraction
        return {"entity_name": None} if message_type == "Non Financial" else {}
    
    # Get field definitions for this type
    field_definitions = type_field_mapping.get(message_type, {})
    if not field_definitions:
        return {}
    
    # Create the prompt with specific field instructions
    field_instructions = "\n".join([
        f"- **{field_name}**: {description}"
        for field_name, description in field_definitions.items()
    ])
    
    prompt = ChatPromptTemplate.from_messages(
        [
            (
                "system",
                f"""
                You are an information extraction system. Your task is to extract specific information from a financial message that has been classified as type: "{message_type}".

                Extract ONLY the following fields from the message. Be conservative - only extract information you are very confident about. If you're not sure about a field, set it to null.

                **Fields to extract:**
                {field_instructions}

                **Important Guidelines:**
                - Only extract information that is explicitly present in the message
                - Be very conservative - when in doubt, set the field to null
                - Extract information exactly as it appears in the message
                - For masked numbers, include the entire masked string including X's
                - For amounts, extract only the numeric value without currency symbols
                - Return the result as a JSON object with the field names as keys
                - Json should always be valid and parsable

                If no relevant information is found for any field, return all fields as null.
                """,
            ),
            ("human", "Extract information from the following message:\n\n---\n{message_body}\n---"),
        ]
    )

    # Create dynamic model for this type
    DynamicModel = create_dynamic_extraction_model(message_type, field_definitions)
    chain = prompt | llm.with_structured_output(DynamicModel)

    try:
        message_body = message.get("body", "")
        if not message_body:
            return {field: None for field in field_definitions.keys()}
        
        result = await chain.ainvoke({"message_body": message_body})
        return result.model_dump()
    except Exception as e:
        return {field: None for field in field_definitions.keys()}

async def classify_and_extract_message(llm: ChatOpenAI, message: dict, type_field_mapping: dict) -> List[dict]:
    """
    Main function that combines both classification and extraction calls.
    Now returns multiple rows for messages with multiple classifications.

    Args:
        llm: The initialized ChatOpenAI model instance.
        message: A dictionary representing a single message.
        type_field_mapping: The field mapping from the JSON file.

    Returns:
        A list of dictionaries, one for each classification of the message.
    """
    # Step 1: Classify the message (now returns multiple classifications)
    classifications = await classify_message(llm, message)
    
    results = []
    
    # Step 2: Extract information for each classification
    for classification in classifications:
        message_type = classification["classified_type"]
        extraction_result = await extract_information(llm, message, message_type, type_field_mapping)
        
        # Combine original message with this specific classification and extraction
        combined_result = {
            **message,
            **classification,
            **extraction_result
        }
        results.append(combined_result)
    
    return results

async def main():
    """
    Main function to load messages, classify them in parallel, and save results to a CSV file.
    """
    # Initialize the LLM
    try:
        llm = ChatOpenAI(model="gpt-4o-mini", temperature=0)
    except ImportError:
        print("Error: The 'openai' library is not installed. Please run 'pip install langchain-openai'.")
        return
    except Exception as e:
        print(f"Error initializing the language model: {e}")
        print("Please ensure your OPENAI_API_KEY environment variable is set correctly.")
        return

    # Load the type-field mapping from JSON file
    try:
        with open("type_fieldname_mapping.json", "r", encoding="utf-8") as f:
            type_field_mapping = json.load(f)
    except FileNotFoundError:
        print("Error: 'type_fieldname_mapping.json' not found.")
        return
    except json.JSONDecodeError:
        print("Error: Could not decode JSON from 'type_fieldname_mapping.json'.")
        return

    # Load messages from the JSON file
    try:
        with open("transactional_sms.json", "r", encoding="utf-8") as f:
            messages = json.load(f)
    except FileNotFoundError:
        print("Error: 'transactional_sms.json' not found. Please create this file and populate it with your message data.")
        return
    except json.JSONDecodeError:
        print("Error: Could not decode JSON from 'transactional_sms.json'. Please ensure it's a valid JSON file.")
        return

    print(f"Loaded {len(messages)} messages. Starting classification and extraction...")

    # Create a list of concurrent tasks for message classification and extraction
    tasks = [classify_and_extract_message(llm, msg, type_field_mapping) for msg in messages]
    message_results = await asyncio.gather(*tasks)
    
    # Flatten the results since each message can now have multiple classifications
    processed_messages = []
    for result_list in message_results:
        processed_messages.extend(result_list)

    print(f"Classification and extraction complete. Generated {len(processed_messages)} rows from {len(messages)} messages. Writing to CSV...")

    # Get all possible field names from the type mapping
    all_extraction_fields = set()
    for type_fields in type_field_mapping.values():
        all_extraction_fields.update(type_fields.keys())

    # Define the headers for the output CSV file
    fieldnames = [
        "from", "date", "body", 
        "classified_type", "classified_subtype", "classified_infotype",
        "error_message"
    ] + sorted(list(all_extraction_fields))

    # Write the processed messages to a CSV file
    with open("classified_messages_output5.csv", "w", newline="", encoding="utf-8") as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames, extrasaction='ignore')
        writer.writeheader()
        writer.writerows(processed_messages)

    print("Successfully classified and extracted information from messages and saved the output to 'classified_messages_output5.csv'")

if __name__ == "__main__":
    # To run this script:
    # 1. Save it as a Python file (e.g., 'run_classifier.py').
    # 2. Ensure you have a 'messages.json' file (with the structure you provided) in the same directory.
    # 3. Set your OpenAI API key as an environment variable:
    #    - On Linux/macOS: export OPENAI_API_KEY='your_key_here'
    #    - On Windows: set OPENAI_API_KEY=your_key_here
    # 4. Run the script from your terminal: python run_classifier.py
    os.environ["OPENAI_API_KEY"] = "***********************************************************************************************************************************************************************"  # Replace with your actual OpenAI API key
    start_time = time.time()
    print("Starting classification and information extraction process...")
    asyncio.run(main())
    end_time = time.time()
    print(f"Classification and extraction process completed in {end_time - start_time:.2f} seconds")