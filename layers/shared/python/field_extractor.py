"""
Field extraction utilities for SMS processing pipeline
Based on the existing field_extractors.py implementation
"""
import re
import asyncio
from typing import Optional, List, Dict, Any
from collections import Counter

def normalize_amount(amount_str: str) -> Optional[str]:
    """Normalize amount string"""
    if not amount_str:
        return None
    
    # Remove currency symbols and extra spaces
    amount_str = re.sub(r'[^\d\.,]', '', amount_str)
    
    # Handle different decimal formats
    if ',' in amount_str and '.' in amount_str:
        # Format like 1,234.56
        amount_str = amount_str.replace(',', '')
    elif ',' in amount_str:
        # Could be 1,234 or 1,56 (European format)
        parts = amount_str.split(',')
        if len(parts[-1]) <= 2:  # Likely decimal
            amount_str = amount_str.replace(',', '.')
        else:  # Likely thousands separator
            amount_str = amount_str.replace(',', '')
    
    try:
        float(amount_str)
        return amount_str
    except ValueError:
        return None

def normalize_date(date_str: str) -> Optional[str]:
    """Normalize date string"""
    if not date_str:
        return None
    
    # Basic date normalization
    date_str = date_str.strip()
    
    # Handle different date formats
    date_patterns = [
        r'\d{1,2}[-/]\d{1,2}[-/]\d{2,4}',
        r'\d{1,2}\s+\w+\s+\d{2,4}',
        r'\w+\s+\d{1,2},?\s+\d{2,4}'
    ]
    
    for pattern in date_patterns:
        if re.search(pattern, date_str):
            return date_str
    
    return None

class FieldExtractor:
    """
    Field extraction class with multiple regex patterns per field.
    Uses frequency-based matching to select the most confident value.
    """
    
    def __init__(self):
        self._compile_patterns()
    
    def _compile_patterns(self):
        """Compile all regex patterns for field extraction"""
        
        # Amount patterns
        self.amount_patterns = [
            re.compile(r'(?:rs\.?|inr|₹)\s*(\d+(?:,\d{3})*(?:\.\d{2})?)', re.IGNORECASE),
            re.compile(r'(\d+(?:,\d{3})*(?:\.\d{2})?)\s*(?:rs\.?|inr|₹)', re.IGNORECASE),
            re.compile(r'amount[:\s]*(?:rs\.?|inr|₹)?\s*(\d+(?:,\d{3})*(?:\.\d{2})?)', re.IGNORECASE),
            re.compile(r'(?:debited|credited|paid|received)[:\s]*(?:rs\.?|inr|₹)?\s*(\d+(?:,\d{3})*(?:\.\d{2})?)', re.IGNORECASE),
            re.compile(r'(\d+(?:,\d{3})*(?:\.\d{2})?)', re.IGNORECASE)  # Generic number pattern
        ]
        
        # Date patterns
        self.date_patterns = [
            re.compile(r'(\d{1,2}[-/]\d{1,2}[-/]\d{2,4})', re.IGNORECASE),
            re.compile(r'(\d{1,2}\s+(?:jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)\w*\s+\d{2,4})', re.IGNORECASE),
            re.compile(r'((?:jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)\w*\s+\d{1,2},?\s+\d{2,4})', re.IGNORECASE),
            re.compile(r'on\s+(\d{1,2}[-/]\d{1,2}[-/]\d{2,4})', re.IGNORECASE),
            re.compile(r'date[:\s]*(\d{1,2}[-/]\d{1,2}[-/]\d{2,4})', re.IGNORECASE)
        ]
        
        # Account number patterns
        self.account_patterns = [
            re.compile(r'a/c[:\s]*(\w+)', re.IGNORECASE),
            re.compile(r'account[:\s]*(\w+)', re.IGNORECASE),
            re.compile(r'ac[:\s]*(\w+)', re.IGNORECASE),
            re.compile(r'(\w*\d{4,})', re.IGNORECASE)  # Generic account-like pattern
        ]
        
        # Bank name patterns
        self.bank_patterns = [
            re.compile(r'(state bank|sbi|hdfc|icici|axis|kotak|pnb|bob|canara|union|indian|central)\s*bank', re.IGNORECASE),
            re.compile(r'(paytm|phonepe|gpay|google pay|bhim|paypal)', re.IGNORECASE),
            re.compile(r'bank[:\s]*([a-z\s]+)', re.IGNORECASE)
        ]
        
        # Transaction reference patterns
        self.txn_ref_patterns = [
            re.compile(r'ref[:\s]*(\w+)', re.IGNORECASE),
            re.compile(r'txn[:\s]*(\w+)', re.IGNORECASE),
            re.compile(r'transaction[:\s]*(\w+)', re.IGNORECASE),
            re.compile(r'utr[:\s]*(\w+)', re.IGNORECASE),
            re.compile(r'(\w+\d{6,})', re.IGNORECASE)  # Generic reference pattern
        ]
        
        # UPI ID patterns
        self.upi_patterns = [
            re.compile(r'(\w+@\w+)', re.IGNORECASE),
            re.compile(r'upi[:\s]*(\w+@\w+)', re.IGNORECASE),
            re.compile(r'vpa[:\s]*(\w+@\w+)', re.IGNORECASE)
        ]
        
        # Card number patterns (last 4 digits)
        self.card_patterns = [
            re.compile(r'card[:\s]*\w*(\d{4})', re.IGNORECASE),
            re.compile(r'ending[:\s]*(\d{4})', re.IGNORECASE),
            re.compile(r'xxxx[:\s]*(\d{4})', re.IGNORECASE)
        ]
    
    async def _extract_with_patterns(self, text: str, patterns: List[re.Pattern], 
                                   normalizer=None) -> Optional[str]:
        """
        Extract field using multiple patterns and return most frequent match.
        """
        matches = []
        
        for pattern in patterns:
            found = pattern.findall(text)
            if found:
                for match in found:
                    if isinstance(match, tuple):
                        # Handle patterns with multiple groups
                        match = ' '.join(match).strip()
                    matches.append(str(match).strip())
        
        if not matches:
            return None
        
        # Apply normalizer if provided
        if normalizer:
            matches = [normalizer(match) for match in matches if normalizer(match)]
        
        # Return most frequent match
        if matches:
            counter = Counter(matches)
            return counter.most_common(1)[0][0]
        
        return None
    
    async def extract_amount(self, text: str) -> Optional[str]:
        """Extract amount with multiple regex patterns."""
        return await self._extract_with_patterns(text, self.amount_patterns, normalize_amount)
    
    async def extract_date(self, text: str) -> Optional[str]:
        """Extract date with multiple regex patterns."""
        return await self._extract_with_patterns(text, self.date_patterns, normalize_date)
    
    async def extract_account_number(self, text: str) -> Optional[str]:
        """Extract account number with multiple regex patterns."""
        return await self._extract_with_patterns(text, self.account_patterns)
    
    async def extract_bank_name(self, text: str) -> Optional[str]:
        """Extract bank name with multiple regex patterns."""
        return await self._extract_with_patterns(text, self.bank_patterns)
    
    async def extract_transaction_ref(self, text: str) -> Optional[str]:
        """Extract transaction reference with multiple regex patterns."""
        return await self._extract_with_patterns(text, self.txn_ref_patterns)
    
    async def extract_upi_id(self, text: str) -> Optional[str]:
        """Extract UPI ID with multiple regex patterns."""
        return await self._extract_with_patterns(text, self.upi_patterns)
    
    async def extract_card_number(self, text: str) -> Optional[str]:
        """Extract card number (last 4 digits) with multiple regex patterns."""
        return await self._extract_with_patterns(text, self.card_patterns)
    
    async def extract_all_fields(self, text: str) -> Dict[str, Optional[str]]:
        """Extract all fields from text"""
        return {
            'amount': await self.extract_amount(text),
            'date': await self.extract_date(text),
            'account_number': await self.extract_account_number(text),
            'bank_name': await self.extract_bank_name(text),
            'transaction_ref': await self.extract_transaction_ref(text),
            'upi_id': await self.extract_upi_id(text),
            'card_number': await self.extract_card_number(text)
        }

# Global field extractor instance
field_extractor = FieldExtractor()
