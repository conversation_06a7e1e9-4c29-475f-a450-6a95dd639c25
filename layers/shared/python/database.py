"""
Database utilities for SMS processing pipeline
"""
import os
import json
import logging
from typing import List, Dict, Any, Optional, <PERSON>ple
import psycopg2
from psycopg2.extras import <PERSON>D<PERSON><PERSON>urs<PERSON>, Json
from psycopg2.pool import SimpleConnectionPool
import uuid
from datetime import datetime

logger = logging.getLogger(__name__)

class DatabaseManager:
    """Database manager for PostgreSQL operations"""
    
    def __init__(self):
        self.connection_pool = None
    
    def _initialize_connection_pool(self):
        """Initialize connection pool"""
        try:
            self.connection_pool = SimpleConnectionPool(
                minconn=1,
                maxconn=10,
                host=os.environ['DB_HOST'],
                port=os.environ['DB_PORT'],
                database=os.environ['DB_NAME'],
                user=os.environ['DB_USERNAME'],
                password=os.environ['DB_PASSWORD'],
                options=f"-c search_path={os.environ.get('DB_SCHEMA', 'customer_investigation_kb')}"
            )
            logger.info("Database connection pool initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize database connection pool: {str(e)}")
            raise
    
    def get_connection(self):
        """Get a connection from the pool"""
        if not self.connection_pool:
            self._initialize_connection_pool()
        return self.connection_pool.getconn()
    
    def return_connection(self, conn):
        """Return a connection to the pool"""
        if self.connection_pool and conn:
            self.connection_pool.putconn(conn)
    
    def execute_query(self, query: str, params: tuple = None, fetch: bool = False) -> Optional[List[Dict]]:
        """Execute a query and optionally fetch results"""
        conn = None
        try:
            conn = self.get_connection()
            with conn.cursor(cursor_factory=RealDictCursor) as cursor:
                cursor.execute(query, params)
                if fetch:
                    return [dict(row) for row in cursor.fetchall()]
                conn.commit()
                return None
        except Exception as e:
            if conn:
                conn.rollback()
            logger.error(f"Database query failed: {str(e)}")
            raise
        finally:
            if conn:
                self.return_connection(conn)
    
    def insert_sms_message(self, customer_id: str, message_text: str,
                          message_type: str, s3_path: str = None,
                          classification_metadata: dict = None) -> str:
        """Insert a new SMS message and return the message_id"""
        query = """
        INSERT INTO sms_messages (customer_id, message_text, message_type, s3_path, classification)
        VALUES (%s, %s, %s, %s, %s)
        RETURNING message_id
        """
        from psycopg2.extras import Json
        result = self.execute_query(
            query,
            (customer_id, message_text, message_type, s3_path, Json(classification_metadata or {})),
            fetch=True
        )
        return str(result[0]['message_id'])
    
    def get_messages_by_ids(self, message_ids: List[str]) -> List[Dict]:
        """Get messages by their IDs"""
        if not message_ids:
            return []
        
        placeholders = ','.join(['%s'] * len(message_ids))
        query = f"""
        SELECT message_id, customer_id, message_text, message_type, s3_path, processed
        FROM sms_messages 
        WHERE message_id IN ({placeholders})
        """
        return self.execute_query(query, tuple(message_ids), fetch=True)
    
    def update_message_analysis(self, message_id: str, analysis_result: Dict, 
                               classification: Dict = None):
        """Update message with analysis results"""
        query = """
        UPDATE sms_messages 
        SET analysis_result = %s, classification = %s, processed = TRUE, processed_at = CURRENT_TIMESTAMP
        WHERE message_id = %s
        """
        self.execute_query(query, (Json(analysis_result), Json(classification), message_id))
    
    def create_batch_processing(self, customer_id: str, message_ids: List[str]) -> str:
        """Create a new batch processing record"""
        batch_id = str(uuid.uuid4())
        query = """
        INSERT INTO batch_processing (batch_id, customer_id, message_ids, batch_size, status)
        VALUES (%s, %s, %s, %s, 'pending')
        RETURNING batch_id
        """
        result = self.execute_query(
            query, 
            (batch_id, customer_id, message_ids, len(message_ids)), 
            fetch=True
        )
        return str(result[0]['batch_id'])
    
    def update_batch_status(self, batch_id: str, status: str, error_message: str = None):
        """Update batch processing status"""
        if status == 'processing':
            query = """
            UPDATE batch_processing 
            SET status = %s, started_at = CURRENT_TIMESTAMP
            WHERE batch_id = %s
            """
            self.execute_query(query, (status, batch_id))
        elif status == 'completed':
            query = """
            UPDATE batch_processing 
            SET status = %s, completed_at = CURRENT_TIMESTAMP
            WHERE batch_id = %s
            """
            self.execute_query(query, (status, batch_id))
        elif status == 'failed':
            query = """
            UPDATE batch_processing 
            SET status = %s, completed_at = CURRENT_TIMESTAMP, error_message = %s
            WHERE batch_id = %s
            """
            self.execute_query(query, (status, error_message, batch_id))
    
    def initialize_customer_status(self, customer_id: str, total_messages: int, 
                                 financial_count: int, non_financial_count: int):
        """Initialize customer processing status"""
        query = """
        SELECT initialize_customer_processing_status(%s, %s, %s, %s)
        """
        self.execute_query(query, (customer_id, total_messages, financial_count, non_financial_count))
    
    def get_customer_processing_status(self, customer_id: str) -> Optional[Dict]:
        """Get customer processing status"""
        query = """
        SELECT customer_id, total_messages, processed_messages, financial_messages, 
               non_financial_messages, status, started_at, completed_at, 
               external_api_notified, external_api_notified_at
        FROM customer_processing_status 
        WHERE customer_id = %s
        """
        result = self.execute_query(query, (customer_id,), fetch=True)
        return result[0] if result else None
    
    def check_all_messages_processed(self, customer_id: str) -> bool:
        """Check if all messages for a customer have been processed"""
        status = self.get_customer_processing_status(customer_id)
        if not status:
            return False
        return status['processed_messages'] >= status['total_messages']
    
    def mark_external_api_notified(self, customer_id: str):
        """Mark that external API has been notified for this customer"""
        query = """
        UPDATE customer_processing_status 
        SET external_api_notified = TRUE, external_api_notified_at = CURRENT_TIMESTAMP
        WHERE customer_id = %s
        """
        self.execute_query(query, (customer_id,))
    
    def log_processing_event(self, customer_id: str, log_level: str, message: str,
                           batch_id: str = None, message_id: str = None, 
                           metadata: Dict = None):
        """Log a processing event"""
        query = """
        SELECT log_processing_event(%s, %s, %s, %s, %s, %s)
        """
        self.execute_query(query, (
            customer_id, batch_id, message_id, log_level, message, 
            Json(metadata) if metadata else None
        ))
    
    def get_unprocessed_message_count(self, customer_id: str) -> int:
        """Get count of unprocessed messages for a customer"""
        query = """
        SELECT COUNT(*) as count
        FROM sms_messages 
        WHERE customer_id = %s AND processed = FALSE
        """
        result = self.execute_query(query, (customer_id,), fetch=True)
        return result[0]['count'] if result else 0

# Global database manager instance
db_manager = DatabaseManager()
