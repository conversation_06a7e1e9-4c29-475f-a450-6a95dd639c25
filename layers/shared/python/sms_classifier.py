"""
SMS Classification utilities for the processing pipeline
"""
import re
import logging
from typing import Dict, List, Tuple, Any

logger = logging.getLogger(__name__)

def clean_text(text: str) -> str:
    """Clean and normalize text for processing"""
    if not text:
        return ""
    
    # Convert to lowercase and strip whitespace
    text = text.lower().strip()
    
    # Remove extra whitespace
    text = re.sub(r'\s+', ' ', text)
    
    # Remove special characters but keep basic punctuation
    text = re.sub(r'[^\w\s\.\,\-\:\;\(\)\[\]\/\@\#\$\%\&\*\+\=\<\>\?\!\~\`\'\"]', ' ', text)
    
    return text

class SMSClassifier:
    """
    SMS classification logic to determine if SMS is financial or non-financial
    Enhanced version matching the existing hybrid SMS processor
    """

    def __init__(self):
        self._setup_classification_rules()
        self._setup_detailed_patterns()
    
    def _setup_classification_rules(self):
        """Setup classification rules with keywords and patterns"""
        
        # Financial keywords and patterns
        self.financial_keywords = [
            # Banking terms
            'bank', 'account', 'balance', 'transaction', 'transfer', 'deposit', 'withdrawal',
            'credit', 'debit', 'payment', 'amount', 'rupees', 'rs', 'inr',
            
            # Payment methods
            'upi', 'card', 'atm', 'pos', 'neft', 'rtgs', 'imps', 'paytm', 'phonepe', 
            'gpay', 'google pay', 'bhim', 'paypal',
            
            # Financial institutions
            'sbi', 'hdfc', 'icici', 'axis', 'kotak', 'pnb', 'bob', 'canara',
            
            # Transaction types
            'purchase', 'refund', 'cashback', 'reward', 'loan', 'emi', 'insurance',
            'investment', 'mutual fund', 'fd', 'rd', 'savings',
            
            # Security
            'otp', 'pin', 'cvv', 'security code', 'verification',
            
            # Bills and utilities
            'bill', 'recharge', 'electricity', 'gas', 'water', 'mobile', 'broadband'
        ]
        
        self.financial_patterns = [
            # Amount patterns
            r'rs\.?\s*\d+',
            r'inr\s*\d+',
            r'amount.*\d+',
            r'\d+\.\d{2}',
            
            # Account patterns
            r'a/c\s*\w+',
            r'account.*\w{4}',
            r'card.*\w{4}',
            
            # Transaction patterns
            r'txn\s*\w+',
            r'ref\s*no',
            r'transaction.*\w+',
            
            # UPI patterns
            r'upi\s+(?:txn|transaction|payment)',
            r'paid\s+(?:via|through|using)\s+upi',
            
            # Banking patterns
            r'(?:debited|credited)\s+by\s+rs',
            r'available\s+balance',
            r'minimum\s+balance'
        ]

        # Non-financial keywords (promotional, informational, etc.)
        self.non_financial_keywords = [
            'offer', 'discount', 'sale', 'free', 'win', 'congratulations',
            'lucky', 'prize', 'gift', 'bonus', 'cashback offer',
            'weather', 'news', 'update', 'reminder', 'appointment',
            'delivery', 'order', 'booking', 'confirmation',
            'birthday', 'anniversary', 'festival', 'holiday'
        ]

    def _setup_detailed_patterns(self):
        """Setup detailed classification patterns matching the existing classifier"""
        # Transaction type patterns
        self.transaction_patterns = {
            'debit': [
                r'debited|deducted|withdrawn|charged|paid',
                r'purchase|payment|transfer|withdrawal',
                r'atm|pos|online|card'
            ],
            'credit': [
                r'credited|deposited|received|added',
                r'salary|refund|cashback|interest',
                r'neft|rtgs|imps|upi.*credited'
            ],
            'balance': [
                r'balance|bal|available',
                r'account.*balance|bal.*enquiry'
            ]
        }

        # Bank and financial institution patterns
        self.bank_patterns = [
            r'(?:state bank|sbi|hdfc|icici|axis|kotak|pnb|bob|canara|union|indian|central)',
            r'(?:bank|banking|banker)',
            r'(?:credit card|debit card|card)',
            r'(?:paytm|phonepe|gpay|google pay|bhim|paypal)'
        ]
    
    def classify_message(self, message_text: str) -> Dict[str, Any]:
        """
        Classify SMS message as financial or non-financial
        
        Returns:
            Dict containing classification results
        """
        if not message_text:
            return {
                'message_type': 'non-financial',
                'confidence': 0.0,
                'matched_keywords': [],
                'matched_patterns': [],
                'reasoning': 'Empty message'
            }
        
        cleaned_text = clean_text(message_text)
        
        # Check for financial keywords
        financial_keyword_matches = []
        for keyword in self.financial_keywords:
            if keyword in cleaned_text:
                financial_keyword_matches.append(keyword)
        
        # Check for financial patterns
        financial_pattern_matches = []
        for pattern in self.financial_patterns:
            if re.search(pattern, cleaned_text):
                financial_pattern_matches.append(pattern)
        
        # Check for non-financial keywords
        non_financial_keyword_matches = []
        for keyword in self.non_financial_keywords:
            if keyword in cleaned_text:
                non_financial_keyword_matches.append(keyword)
        
        # Calculate scores
        financial_score = len(financial_keyword_matches) + len(financial_pattern_matches)
        non_financial_score = len(non_financial_keyword_matches)
        
        # Determine classification
        if financial_score > non_financial_score and financial_score > 0:
            message_type = 'financial'
            confidence = min(financial_score / (financial_score + non_financial_score + 1), 0.95)
            reasoning = f"Found {financial_score} financial indicators"
        elif non_financial_score > 0:
            message_type = 'non-financial'
            confidence = min(non_financial_score / (financial_score + non_financial_score + 1), 0.95)
            reasoning = f"Found {non_financial_score} non-financial indicators"
        else:
            # Default to non-financial if no clear indicators
            message_type = 'non-financial'
            confidence = 0.3
            reasoning = "No clear financial indicators found"
        
        return {
            'message_type': message_type,
            'confidence': confidence,
            'financial_keywords': financial_keyword_matches,
            'financial_patterns': financial_pattern_matches,
            'non_financial_keywords': non_financial_keyword_matches,
            'financial_score': financial_score,
            'non_financial_score': non_financial_score,
            'reasoning': reasoning
        }
    
    def classify_sms(self, message_text: str) -> Dict[str, Any]:
        """
        Enhanced SMS classification matching the existing hybrid processor
        Returns detailed classification with type, subtype, and info type
        """
        if not message_text:
            return {
                'classification': 'non-financial',
                'sms_type': 'Non Financial',
                'sms_event_subtype': 'Unknown',
                'sms_info_type': 'Unknown',
                'confidence': 0.0,
                'reasoning': 'Empty message'
            }

        cleaned_text = clean_text(message_text)

        # Check for financial indicators
        financial_score = self._calculate_financial_score(cleaned_text)
        non_financial_score = self._calculate_non_financial_score(cleaned_text)

        if financial_score > non_financial_score and financial_score > 0:
            # Detailed financial classification
            return self._classify_financial_message(cleaned_text, financial_score)
        else:
            # Non-financial classification
            return {
                'classification': 'non-financial',
                'sms_type': 'Non Financial',
                'sms_event_subtype': 'Promotional' if 'offer' in cleaned_text or 'discount' in cleaned_text else 'Informational',
                'sms_info_type': 'General',
                'confidence': min(non_financial_score / (financial_score + non_financial_score + 1), 0.95),
                'reasoning': f"Found {non_financial_score} non-financial indicators"
            }

    def _calculate_financial_score(self, text: str) -> int:
        """Calculate financial score based on keywords and patterns"""
        score = 0

        # Check financial keywords
        for keyword in self.financial_keywords:
            if keyword in text:
                score += 1

        # Check financial patterns
        for pattern in self.financial_patterns:
            if re.search(pattern, text):
                score += 2  # Patterns get higher weight

        # Check bank patterns
        for pattern in self.bank_patterns:
            if re.search(pattern, text):
                score += 1

        return score

    def _calculate_non_financial_score(self, text: str) -> int:
        """Calculate non-financial score"""
        score = 0
        for keyword in self.non_financial_keywords:
            if keyword in text:
                score += 1
        return score

    def _classify_financial_message(self, text: str, score: int) -> Dict[str, Any]:
        """Classify financial message into detailed categories"""
        # Check transaction type
        if any(re.search(pattern, text) for pattern in self.transaction_patterns['debit']):
            sms_type = 'Debit'
            sms_event_subtype = self._get_debit_subtype(text)
        elif any(re.search(pattern, text) for pattern in self.transaction_patterns['credit']):
            sms_type = 'Credit'
            sms_event_subtype = self._get_credit_subtype(text)
        elif any(re.search(pattern, text) for pattern in self.transaction_patterns['balance']):
            sms_type = 'Balance'
            sms_event_subtype = 'Enquiry'
        else:
            sms_type = 'Financial'
            sms_event_subtype = 'Transaction'

        # Determine info type
        sms_info_type = self._get_info_type(text)

        return {
            'classification': 'financial',
            'sms_type': sms_type,
            'sms_event_subtype': sms_event_subtype,
            'sms_info_type': sms_info_type,
            'confidence': min(score / (score + 1), 0.95),
            'reasoning': f"Found {score} financial indicators"
        }

    def _get_debit_subtype(self, text: str) -> str:
        """Get debit transaction subtype"""
        if 'atm' in text:
            return 'ATM Withdrawal'
        elif 'pos' in text or 'purchase' in text:
            return 'POS Transaction'
        elif 'transfer' in text:
            return 'Fund Transfer'
        elif 'payment' in text:
            return 'Payment'
        else:
            return 'Debit Transaction'

    def _get_credit_subtype(self, text: str) -> str:
        """Get credit transaction subtype"""
        if 'salary' in text:
            return 'Salary Credit'
        elif 'refund' in text:
            return 'Refund'
        elif 'interest' in text:
            return 'Interest Credit'
        elif 'cashback' in text:
            return 'Cashback'
        else:
            return 'Credit Transaction'

    def _get_info_type(self, text: str) -> str:
        """Get information type"""
        if 'otp' in text or 'verification' in text:
            return 'Security'
        elif 'alert' in text or 'notification' in text:
            return 'Alert'
        else:
            return 'Transaction'

    def validate_financial_classification(self, classification: Dict, extracted_amount: str = None) -> Dict:
        """Validate financial classification based on extracted amount"""
        # Make a copy to avoid modifying the original
        validated_classification = classification.copy()

        if validated_classification['classification'] == 'financial' and not extracted_amount:
            # If classified as financial but no amount found, reduce confidence
            validated_classification['confidence'] *= 0.7
            if 'reasoning' in validated_classification:
                validated_classification['reasoning'] += " (no amount found)"
            else:
                validated_classification['reasoning'] = "No amount found"
        elif validated_classification['classification'] == 'non-financial' and extracted_amount:
            # If classified as non-financial but amount found, might be financial
            validated_classification['classification'] = 'financial'
            validated_classification['sms_type'] = 'Financial'
            validated_classification['confidence'] = 0.6
            validated_classification['reasoning'] = "Amount found in non-financial message"

        return validated_classification

    def batch_classify(self, messages: List[str]) -> List[Dict[str, Any]]:
        """Classify multiple messages"""
        return [self.classify_message(msg) for msg in messages]

# Global classifier instance
sms_classifier = SMSClassifier()
