"""
OpenAI integration for SMS processing pipeline
Based on the existing openai_label_generation.py implementation
"""
import json
import asyncio
import aiohttp
from typing import Dict, List, Any, Optional
import logging

logger = logging.getLogger(__name__)

class OpenAIIntegration:
    """
    OpenAI integration for SMS classification and field extraction
    """
    
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = "https://api.openai.com/v1/chat/completions"
        self.model = "gpt-4o-mini"
        
        # Load type-field mapping
        self.type_field_mapping = self._get_type_field_mapping()
    
    def _get_type_field_mapping(self) -> Dict[str, Dict[str, str]]:
        """Get type-field mapping for structured extraction"""
        return {
            "Debit": {
                "amount": "The transaction amount",
                "date": "The transaction date",
                "account_number": "The account number (last 4 digits)",
                "merchant_name": "The merchant or payee name",
                "transaction_ref": "The transaction reference number",
                "card_number": "The card number (last 4 digits)",
                "transaction_type": "The type of debit transaction"
            },
            "Credit": {
                "amount": "The transaction amount",
                "date": "The transaction date",
                "account_number": "The account number (last 4 digits)",
                "sender_name": "The sender or payer name",
                "transaction_ref": "The transaction reference number",
                "upi_id": "The UPI ID if applicable",
                "transaction_type": "The type of credit transaction"
            },
            "Balance": {
                "amount": "The account balance amount",
                "date": "The balance inquiry date",
                "account_number": "The account number (last 4 digits)",
                "available_balance": "The available balance",
                "transaction_ref": "The inquiry reference number"
            },
            "Financial": {
                "amount": "Any monetary amount mentioned",
                "date": "Any date mentioned",
                "account_number": "Any account number mentioned",
                "transaction_ref": "Any reference number mentioned",
                "financial_entity": "The financial institution or service"
            }
        }
    
    async def classify_message(self, message_text: str) -> List[Dict[str, Any]]:
        """
        Classify SMS message using OpenAI
        Returns multiple possible classifications with confidence scores
        """
        prompt = f"""
        Classify the following SMS message into one or more financial categories.
        
        Message: "{message_text}"
        
        Possible categories:
        - Debit: Money debited/withdrawn from account
        - Credit: Money credited/deposited to account  
        - Balance: Account balance inquiry or notification
        - Financial: Other financial services (OTP, alerts, etc.)
        - Non Financial: Non-financial messages (promotional, informational)
        
        For each classification, provide:
        1. classified_type: The main category
        2. classified_subtype: More specific subcategory
        3. classified_infotype: Information type (Transaction, Alert, Security, etc.)
        4. confidence: Confidence score (0.0 to 1.0)
        
        Return as JSON array with multiple classifications if applicable.
        Only include classifications with confidence > 0.7.
        """
        
        try:
            response = await self._call_openai_api(prompt, message_text)
            
            if response and 'choices' in response:
                content = response['choices'][0]['message']['content']
                try:
                    classifications = json.loads(content)
                    if isinstance(classifications, dict):
                        classifications = [classifications]
                    
                    # Filter high confidence classifications
                    high_confidence = [
                        c for c in classifications 
                        if c.get('confidence', 0) > 0.7
                    ]
                    
                    return high_confidence if high_confidence else [{
                        "classified_type": "Non Financial",
                        "classified_subtype": None,
                        "classified_infotype": None,
                        "confidence": 0.5
                    }]
                    
                except json.JSONDecodeError:
                    logger.error(f"Failed to parse OpenAI response: {content}")
                    
        except Exception as e:
            logger.error(f"OpenAI classification failed: {str(e)}")
        
        return [{
            "classified_type": "Error",
            "classified_subtype": "Classification Failed",
            "classified_infotype": None,
            "confidence": 0.0,
            "error_message": "Failed to classify message"
        }]
    
    async def extract_information(self, message_text: str, message_type: str) -> Dict[str, Any]:
        """
        Extract structured information from SMS message based on type
        """
        if message_type not in self.type_field_mapping:
            return {}
        
        field_definitions = self.type_field_mapping[message_type]
        
        # Create extraction prompt
        fields_description = "\n".join([
            f"- {field}: {description}" 
            for field, description in field_definitions.items()
        ])
        
        prompt = f"""
        Extract the following information from this SMS message:
        
        Message: "{message_text}"
        
        Extract these fields:
        {fields_description}
        
        Return as JSON object with field names as keys.
        If a field is not found or not applicable, set its value to null.
        For amounts, return only the numeric value without currency symbols.
        For dates, return in a standardized format.
        """
        
        try:
            response = await self._call_openai_api(prompt, message_text)
            
            if response and 'choices' in response:
                content = response['choices'][0]['message']['content']
                try:
                    extraction_result = json.loads(content)
                    return extraction_result
                except json.JSONDecodeError:
                    logger.error(f"Failed to parse OpenAI extraction response: {content}")
                    
        except Exception as e:
            logger.error(f"OpenAI extraction failed: {str(e)}")
        
        # Return empty dict with null values for all fields
        return {field: None for field in field_definitions.keys()}
    
    async def process_message_complete(self, message_text: str) -> List[Dict[str, Any]]:
        """
        Complete processing: classify and extract information
        """
        # Step 1: Classify the message
        classifications = await self.classify_message(message_text)
        
        results = []
        
        # Step 2: Extract information for each classification
        for classification in classifications:
            message_type = classification["classified_type"]
            extraction_result = await self.extract_information(message_text, message_type)
            
            # Combine classification and extraction
            combined_result = {
                'original_text': message_text,
                **classification,
                **extraction_result
            }
            results.append(combined_result)
        
        return results
    
    async def _call_openai_api(self, prompt: str, message_text: str) -> Optional[Dict]:
        """
        Call OpenAI API with the given prompt
        """
        headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json'
        }
        
        payload = {
            "model": self.model,
            "messages": [
                {
                    "role": "system", 
                    "content": "You are an expert SMS analyzer. Provide accurate analysis in the requested JSON format."
                },
                {
                    "role": "user", 
                    "content": prompt
                }
            ],
            "max_tokens": 1000,
            "temperature": 0.1
        }
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    self.base_url,
                    headers=headers,
                    json=payload,
                    timeout=aiohttp.ClientTimeout(total=30)
                ) as response:
                    if response.status == 200:
                        return await response.json()
                    else:
                        error_text = await response.text()
                        logger.error(f"OpenAI API error {response.status}: {error_text}")
                        return None
                        
        except Exception as e:
            logger.error(f"OpenAI API call failed: {str(e)}")
            return None

# Helper function for backward compatibility
async def call_openai_api_batch(messages: List[str], api_key: str) -> List[Dict[str, Any]]:
    """
    Process multiple messages with OpenAI API
    Returns results in the format expected by the Lambda functions
    """
    openai_integration = OpenAIIntegration(api_key)
    results = []
    
    for message in messages:
        try:
            # Get complete processing results
            processing_results = await openai_integration.process_message_complete(message)
            
            if processing_results:
                # Take the first (highest confidence) result
                result = processing_results[0]
                results.append({
                    'message': message,
                    'analysis': result,
                    'success': True,
                    'error': None
                })
            else:
                results.append({
                    'message': message,
                    'analysis': None,
                    'success': False,
                    'error': 'No analysis results returned'
                })
                
        except Exception as e:
            results.append({
                'message': message,
                'analysis': None,
                'success': False,
                'error': str(e)
            })
    
    return results
