#!/bin/bash

# SMS Processing Pipeline Deployment Script

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${G<PERSON><PERSON>}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if required tools are installed
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    if ! command -v sam &> /dev/null; then
        print_error "AWS SAM CLI is not installed. Please install it first."
        exit 1
    fi
    
    if ! command -v aws &> /dev/null; then
        print_error "AWS CLI is not installed. Please install it first."
        exit 1
    fi
    
    if ! command -v python3 &> /dev/null; then
        print_error "Python 3 is not installed. Please install it first."
        exit 1
    fi
    
    print_status "Prerequisites check passed!"
}

# Validate environment variables
validate_environment() {
    print_status "Validating environment configuration..."
    
    if [ ! -f ".env" ]; then
        print_warning ".env file not found. Creating from template..."
        cp .env.example .env
        print_warning "Please edit .env file with your actual values before deploying."
        exit 1
    fi
    
    # Source environment variables
    source .env
    
    # Check required variables
    required_vars=("DB_HOST" "DB_PASSWORD" "OPENAI_API_KEY" "EXTERNAL_API_ENDPOINT")
    
    for var in "${required_vars[@]}"; do
        if [ -z "${!var}" ]; then
            print_error "Required environment variable $var is not set in .env file"
            exit 1
        fi
    done
    
    print_status "Environment validation passed!"
}

# Build the application
build_application() {
    print_status "Building application..."
    
    # Clean previous builds
    make clean
    
    # Install dependencies
    make install-deps
    
    # Build with SAM
    make sam-build
    
    print_status "Application built successfully!"
}

# Validate SAM template
validate_template() {
    print_status "Validating SAM template..."
    
    make validate
    
    print_status "Template validation passed!"
}

# Deploy the application
deploy_application() {
    print_status "Deploying application..."
    
    # Source environment variables for parameter overrides
    source .env
    
    # Construct parameter overrides
    PARAM_OVERRIDES="Environment=\"${ENVIRONMENT:-dev}\""
    PARAM_OVERRIDES="$PARAM_OVERRIDES DatabaseHost=\"$DB_HOST\""
    PARAM_OVERRIDES="$PARAM_OVERRIDES DatabasePort=\"$DB_PORT\""
    PARAM_OVERRIDES="$PARAM_OVERRIDES DatabaseName=\"$DB_NAME\""
    PARAM_OVERRIDES="$PARAM_OVERRIDES DatabaseUsername=\"$DB_USERNAME\""
    PARAM_OVERRIDES="$PARAM_OVERRIDES DatabasePassword=\"$DB_PASSWORD\""
    PARAM_OVERRIDES="$PARAM_OVERRIDES DatabaseSchema=\"$DB_SCHEMA\""
    PARAM_OVERRIDES="$PARAM_OVERRIDES BatchSize=\"$BATCH_SIZE\""
    PARAM_OVERRIDES="$PARAM_OVERRIDES OpenAIApiKey=\"$OPENAI_API_KEY\""
    PARAM_OVERRIDES="$PARAM_OVERRIDES ExternalApiEndpoint=\"$EXTERNAL_API_ENDPOINT\""
    
    # Deploy based on mode
    if [ "$1" = "guided" ]; then
        print_status "Deploying in guided mode..."
        sam deploy --guided --parameter-overrides "$PARAM_OVERRIDES"
    else
        print_status "Deploying in automatic mode..."
        sam deploy --no-confirm-changeset --no-fail-on-empty-changeset --parameter-overrides "$PARAM_OVERRIDES"
    fi
    
    print_status "Deployment completed successfully!"
}

# Initialize database schema
init_database() {
    print_status "Initializing database schema..."
    
    source .env
    
    # Check if psql is available
    if ! command -v psql &> /dev/null; then
        print_warning "psql is not installed. Please run the schema.sql file manually on your database."
        return
    fi
    
    # Run schema initialization
    PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USERNAME" -d "$DB_NAME" -f schema.sql
    
    print_status "Database schema initialized successfully!"
}

# Main deployment function
main() {
    print_status "Starting SMS Processing Pipeline deployment..."
    
    # Parse command line arguments
    MODE="auto"
    INIT_DB=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --guided)
                MODE="guided"
                shift
                ;;
            --init-db)
                INIT_DB=true
                shift
                ;;
            --help)
                echo "Usage: $0 [OPTIONS]"
                echo "Options:"
                echo "  --guided    Deploy in guided mode (interactive)"
                echo "  --init-db   Initialize database schema after deployment"
                echo "  --help      Show this help message"
                exit 0
                ;;
            *)
                print_error "Unknown option: $1"
                exit 1
                ;;
        esac
    done
    
    # Run deployment steps
    check_prerequisites
    validate_environment
    validate_template
    build_application
    deploy_application "$MODE"
    
    if [ "$INIT_DB" = true ]; then
        init_database
    fi
    
    print_status "SMS Processing Pipeline deployment completed successfully!"
    print_status "Check AWS CloudFormation console for stack details."
    print_status "Monitor CloudWatch Logs for Lambda function execution."
}

# Run main function
main "$@"
