"""
Lambda 2: Message Analysis Function
Processes batches from SMS Batch Queue, analyzes messages using OpenAI API,
stores results in database, and sends completion signals.
"""
import json
import logging
import os
from typing import Dict, List, Any
import asyncio

# Import from shared layer
from database import db_manager
from field_extractor import field_extractor
from openai_integration import OpenAIIntegration
from utils import (
    setup_logging, call_openai_api, send_sqs_message,
    get_environment_variable, validate_message_format,
    create_error_response, create_success_response
)

# Setup logging
setup_logging()
logger = logging.getLogger(__name__)

def lambda_handler(event, context):
    """
    Main Lambda handler for message analysis
    
    Expected SQS message format:
    {
        "customer_id": "customer_123",
        "message_ids": ["msg_id_1", "msg_id_2", ...],
        "batch_id": "batch_uuid",
        "batch_number": 1,
        "total_batches": 5
    }
    """
    try:
        logger.info(f"Processing {len(event.get('Records', []))} SQS messages")
        
        # Process each SQS record
        for record in event.get('Records', []):
            try:
                # Use asyncio to handle async operations
                asyncio.run(process_sqs_record(record))
            except Exception as e:
                logger.error(f"Failed to process SQS record: {str(e)}")
                # Continue processing other records
                continue
        
        return create_success_response(message="Message analysis completed successfully")
    
    except Exception as e:
        logger.error(f"Lambda execution failed: {str(e)}")
        return create_error_response(f"Lambda execution failed: {str(e)}")

async def process_sqs_record(record: Dict[str, Any]):
    """Process a single SQS record"""
    try:
        # Parse SQS message
        message_body = json.loads(record['body'])
        logger.info(f"Processing batch: {message_body}")
        
        # Validate message format
        if not validate_message_format(message_body):
            raise ValueError("Invalid message format")
        
        customer_id = message_body['customer_id']
        message_ids = message_body.get('message_ids', [])
        batch_id = message_body.get('batch_id')
        batch_number = message_body.get('batch_number', 1)
        total_batches = message_body.get('total_batches', 1)
        
        if not message_ids:
            raise ValueError("message_ids is required and cannot be empty")
        
        # Update batch status to processing
        if batch_id:
            db_manager.update_batch_status(batch_id, 'processing')
        
        # Log processing start
        db_manager.log_processing_event(
            customer_id=customer_id,
            batch_id=batch_id,
            log_level='INFO',
            message=f"Started message analysis for batch {batch_number}/{total_batches}",
            metadata={
                'message_count': len(message_ids),
                'batch_id': batch_id
            }
        )
        
        # Fetch messages from database
        logger.info(f"Fetching {len(message_ids)} messages from database")
        messages_data = db_manager.get_messages_by_ids(message_ids)
        
        if not messages_data:
            raise ValueError("No messages found for the given IDs")
        
        # Get OpenAI API key
        openai_api_key = get_environment_variable('OPENAI_API_KEY', required=True)

        # Initialize enhanced OpenAI integration
        openai_integration = OpenAIIntegration(openai_api_key)

        # Process messages with enhanced analysis
        logger.info(f"Analyzing {len(messages_data)} messages with enhanced OpenAI integration")
        analysis_results = []

        for msg_data in messages_data:
            message_text = msg_data['message_text']
            try:
                # Get complete analysis including classification and field extraction
                complete_analysis = await openai_integration.process_message_complete(message_text)

                # Also extract fields using regex-based extractor for comparison
                regex_fields = await field_extractor.extract_all_fields(message_text)

                analysis_results.append({
                    'message': message_text,
                    'analysis': {
                        'openai_analysis': complete_analysis,
                        'regex_extraction': regex_fields,
                        'hybrid_result': combine_analysis_results(complete_analysis, regex_fields)
                    },
                    'success': True,
                    'error': None
                })
            except Exception as e:
                logger.error(f"Failed to analyze message: {str(e)}")
                analysis_results.append({
                    'message': message_text,
                    'analysis': None,
                    'success': False,
                    'error': str(e)
                })
        
        # Store analysis results in database
        successful_analyses = 0
        failed_analyses = 0
        
        for i, (message_data, analysis_result) in enumerate(zip(messages_data, analysis_results)):
            message_id = message_data['message_id']
            
            try:
                if analysis_result['success']:
                    # Store successful analysis
                    db_manager.update_message_analysis(
                        message_id=str(message_id),
                        analysis_result=analysis_result['analysis'],
                        classification={
                            'openai_analysis': True,
                            'analysis_timestamp': analysis_result.get('timestamp'),
                            'confidence': analysis_result['analysis'].get('confidence', 0.8)
                        }
                    )
                    successful_analyses += 1
                    logger.debug(f"Stored analysis for message {message_id}")
                else:
                    # Store failed analysis with error info
                    db_manager.update_message_analysis(
                        message_id=str(message_id),
                        analysis_result={
                            'error': analysis_result['error'],
                            'success': False
                        },
                        classification={
                            'openai_analysis': False,
                            'error': analysis_result['error']
                        }
                    )
                    failed_analyses += 1
                    logger.warning(f"Failed to analyze message {message_id}: {analysis_result['error']}")
            
            except Exception as e:
                logger.error(f"Failed to store analysis for message {message_id}: {str(e)}")
                failed_analyses += 1
        
        # Update batch status
        if batch_id:
            if failed_analyses == 0:
                db_manager.update_batch_status(batch_id, 'completed')
            else:
                error_msg = f"Failed to analyze {failed_analyses} out of {len(message_ids)} messages"
                db_manager.update_batch_status(batch_id, 'failed', error_msg)
        
        # Log batch completion
        db_manager.log_processing_event(
            customer_id=customer_id,
            batch_id=batch_id,
            log_level='INFO',
            message=f"Completed analysis for batch {batch_number}/{total_batches}",
            metadata={
                'successful_analyses': successful_analyses,
                'failed_analyses': failed_analyses,
                'total_messages': len(message_ids)
            }
        )
        
        # Send completion signal
        await send_completion_signal(customer_id)
        
        logger.info(f"Successfully processed batch {batch_number}/{total_batches} for customer {customer_id}")
    
    except Exception as e:
        logger.error(f"Failed to process SQS record: {str(e)}")
        
        # Update batch status to failed
        try:
            message_body = json.loads(record['body'])
            batch_id = message_body.get('batch_id')
            customer_id = message_body.get('customer_id')
            
            if batch_id:
                db_manager.update_batch_status(batch_id, 'failed', str(e))
            
            if customer_id:
                db_manager.log_processing_event(
                    customer_id=customer_id,
                    batch_id=batch_id,
                    log_level='ERROR',
                    message=f"Message analysis failed: {str(e)}",
                    metadata={'error_type': type(e).__name__}
                )
        except:
            pass
        
        raise

def combine_analysis_results(openai_results: List[Dict], regex_fields: Dict) -> Dict:
    """
    Combine OpenAI analysis with regex-based field extraction
    Returns the best hybrid result
    """
    if not openai_results:
        return {
            'classification': 'non-financial',
            'extracted_fields': regex_fields,
            'confidence': 0.3,
            'source': 'regex_only'
        }

    # Take the highest confidence OpenAI result
    best_openai = max(openai_results, key=lambda x: x.get('confidence', 0))

    # Combine fields, preferring OpenAI results but falling back to regex
    combined_fields = {}

    # Start with regex fields as baseline
    for field, value in regex_fields.items():
        if value:
            combined_fields[field] = {
                'value': value,
                'source': 'regex',
                'confidence': 0.7
            }

    # Override with OpenAI fields if available and confident
    for field in ['amount', 'date', 'account_number', 'transaction_ref']:
        if field in best_openai and best_openai[field]:
            combined_fields[field] = {
                'value': best_openai[field],
                'source': 'openai',
                'confidence': best_openai.get('confidence', 0.8)
            }

    return {
        'classification': best_openai.get('classified_type', 'Non Financial'),
        'subtype': best_openai.get('classified_subtype'),
        'info_type': best_openai.get('classified_infotype'),
        'extracted_fields': combined_fields,
        'confidence': best_openai.get('confidence', 0.5),
        'source': 'hybrid',
        'openai_raw': best_openai,
        'regex_raw': regex_fields
    }

async def send_completion_signal(customer_id: str):
    """Send completion signal to Completion Queue"""
    try:
        completion_queue_url = get_environment_variable('COMPLETION_QUEUE_URL', required=True)

        # Prepare completion message
        completion_message = {
            'customer_id': customer_id,
            'event_type': 'batch_completed',
            'timestamp': asyncio.get_event_loop().time()
        }

        # Send to Completion Queue
        message_id = send_sqs_message(completion_queue_url, completion_message)

        logger.info(f"Sent completion signal for customer {customer_id}: {message_id}")

        # Log completion signal
        db_manager.log_processing_event(
            customer_id=customer_id,
            log_level='INFO',
            message=f"Sent completion signal for customer {customer_id}",
            metadata={'sqs_message_id': message_id}
        )

    except Exception as e:
        logger.error(f"Failed to send completion signal: {str(e)}")
        # Don't raise here as the main processing was successful
