"""
Lambda 3: Completion Check Function
Processes completion signals from Completion Queue, checks if all messages
for a customer are processed, and calls external API when complete.
"""
import json
import logging
import os
from typing import Dict, Any
import asyncio

# Import from shared layer
from database import db_manager
from utils import (
    setup_logging, call_external_api, get_environment_variable,
    validate_message_format, create_error_response, create_success_response
)

# Setup logging
setup_logging()
logger = logging.getLogger(__name__)

def lambda_handler(event, context):
    """
    Main Lambda handler for completion check
    
    Expected SQS message format:
    {
        "customer_id": "customer_123",
        "event_type": "batch_completed",
        "timestamp": 1234567890
    }
    """
    try:
        logger.info(f"Processing {len(event.get('Records', []))} SQS messages")
        
        # Process each SQS record
        for record in event.get('Records', []):
            try:
                # Use asyncio to handle async operations
                asyncio.run(process_sqs_record(record))
            except Exception as e:
                logger.error(f"Failed to process SQS record: {str(e)}")
                # Continue processing other records
                continue
        
        return create_success_response(message="Completion check completed successfully")
    
    except Exception as e:
        logger.error(f"Lambda execution failed: {str(e)}")
        return create_error_response(f"Lambda execution failed: {str(e)}")

async def process_sqs_record(record: Dict[str, Any]):
    """Process a single SQS record"""
    try:
        # Parse SQS message
        message_body = json.loads(record['body'])
        logger.info(f"Processing completion check: {message_body}")
        
        # Validate message format
        if not validate_message_format(message_body):
            raise ValueError("Invalid message format")
        
        customer_id = message_body['customer_id']
        event_type = message_body.get('event_type', 'batch_completed')
        
        # Log processing start
        db_manager.log_processing_event(
            customer_id=customer_id,
            log_level='INFO',
            message=f"Started completion check for customer {customer_id}",
            metadata={'event_type': event_type}
        )
        
        # Check if all messages for this customer have been processed
        logger.info(f"Checking completion status for customer {customer_id}")
        
        # Get customer processing status
        customer_status = db_manager.get_customer_processing_status(customer_id)
        
        if not customer_status:
            logger.warning(f"No processing status found for customer {customer_id}")
            return
        
        # Check if already notified
        if customer_status['external_api_notified']:
            logger.info(f"External API already notified for customer {customer_id}")
            return
        
        # Check if all messages are processed
        all_processed = db_manager.check_all_messages_processed(customer_id)
        
        if all_processed:
            logger.info(f"All messages processed for customer {customer_id}, calling external API")
            
            # Get additional data for external API call
            additional_data = {
                'total_messages': customer_status['total_messages'],
                'processed_messages': customer_status['processed_messages'],
                'financial_messages': customer_status['financial_messages'],
                'non_financial_messages': customer_status['non_financial_messages'],
                'processing_started_at': customer_status['started_at'].isoformat() if customer_status['started_at'] else None,
                'processing_completed_at': customer_status['completed_at'].isoformat() if customer_status['completed_at'] else None
            }
            
            # Call external API
            external_api_endpoint = get_environment_variable('EXTERNAL_API_ENDPOINT', required=True)
            success = await call_external_api(external_api_endpoint, customer_id, additional_data)
            
            if success:
                # Mark as notified in database
                db_manager.mark_external_api_notified(customer_id)
                
                # Log successful notification
                db_manager.log_processing_event(
                    customer_id=customer_id,
                    log_level='INFO',
                    message=f"Successfully notified external API for customer {customer_id}",
                    metadata={
                        'external_api_endpoint': external_api_endpoint,
                        'total_messages': customer_status['total_messages'],
                        'processed_messages': customer_status['processed_messages']
                    }
                )
                
                logger.info(f"Successfully completed processing for customer {customer_id}")
            else:
                # Log failed notification
                db_manager.log_processing_event(
                    customer_id=customer_id,
                    log_level='ERROR',
                    message=f"Failed to notify external API for customer {customer_id}",
                    metadata={'external_api_endpoint': external_api_endpoint}
                )
                
                # Raise exception to trigger retry via DLQ
                raise Exception(f"Failed to notify external API for customer {customer_id}")
        else:
            # Not all messages processed yet
            unprocessed_count = db_manager.get_unprocessed_message_count(customer_id)
            
            logger.info(f"Customer {customer_id} still has {unprocessed_count} unprocessed messages")
            
            # Log status update
            db_manager.log_processing_event(
                customer_id=customer_id,
                log_level='INFO',
                message=f"Processing still in progress for customer {customer_id}",
                metadata={
                    'total_messages': customer_status['total_messages'],
                    'processed_messages': customer_status['processed_messages'],
                    'unprocessed_messages': unprocessed_count
                }
            )
    
    except Exception as e:
        logger.error(f"Failed to process SQS record: {str(e)}")
        
        # Log error
        try:
            message_body = json.loads(record['body'])
            customer_id = message_body.get('customer_id')
            if customer_id:
                db_manager.log_processing_event(
                    customer_id=customer_id,
                    log_level='ERROR',
                    message=f"Completion check failed: {str(e)}",
                    metadata={'error_type': type(e).__name__}
                )
        except:
            pass
        
        raise
