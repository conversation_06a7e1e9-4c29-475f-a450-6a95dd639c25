#!/usr/bin/env python3
"""
Main script to run hybrid SMS processing on the SMS backup data.
Combines rule-based financial/non-financial classification with OpenAI field extraction.
"""

import asyncio
import time
import os
from hybrid_sms_processor import process_csv_file

def main():
    """Main function to run hybrid SMS processing."""
    
    # Configuration
    INPUT_CSV = "sms_backup.csv"  # Input CSV file
    OUTPUT_CSV = "hybrid_sms_processed_results.csv"  # Output CSV file
    
    # OpenAI API key - set this to your actual key or set as environment variable
    OPENAI_API_KEY = "***********************************************************************************************************************************************************************"
    
    # Check if input file exists
    if not os.path.exists(INPUT_CSV):
        print(f"Error: Input file '{INPUT_CSV}' not found.")
        print("Please ensure the SMS backup CSV file is in the current directory.")
        return
    
    # Check if type mapping file exists
    if not os.path.exists("type_fieldname_mapping.json"):
        print("Warning: 'type_fieldname_mapping.json' not found.")
        print("OpenAI extraction will be limited without this file.")
    
    print("=" * 60)
    print("HYBRID SMS PROCESSING")
    print("=" * 60)
    print(f"Input file: {INPUT_CSV}")
    print(f"Output file: {OUTPUT_CSV}")
    print(f"OpenAI integration: {'Enabled' if OPENAI_API_KEY else 'Disabled (rule-based only)'}")
    print()
    
    print("Processing approach:")
    print("1. Rule-based classification (financial vs non-financial)")
    print("2. OpenAI field extraction (for financial messages only)")
    print("3. Combined output with both rule-based and OpenAI results")
    print()
    
    # Confirm before processing
    response = input("Do you want to proceed? (y/n): ").strip().lower()
    if response != 'y':
        print("Processing cancelled.")
        return
    
    # Start processing
    start_time = time.time()
    print("\nStarting hybrid SMS processing...")
    
    try:
        asyncio.run(process_csv_file(INPUT_CSV, OUTPUT_CSV, OPENAI_API_KEY))
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        print(f"\n{'='*60}")
        print("PROCESSING COMPLETED SUCCESSFULLY!")
        print(f"{'='*60}")
        print(f"Processing time: {processing_time:.2f} seconds")
        print(f"Output saved to: {OUTPUT_CSV}")
        
        # Check if output file was created and get basic stats
        if os.path.exists(OUTPUT_CSV):
            with open(OUTPUT_CSV, 'r', encoding='utf-8') as f:
                lines = sum(1 for line in f) - 1  # Subtract header
            print(f"Total records processed: {lines}")
        
        print("\nNext steps:")
        print(f"1. Review the results in '{OUTPUT_CSV}'")
        print("2. Compare with previous results if needed")
        print("3. Run tests to validate the output")
        
    except KeyboardInterrupt:
        print("\nProcessing interrupted by user.")
    except Exception as e:
        print(f"\nError during processing: {e}")
        print("Please check the error message and try again.")

if __name__ == "__main__":
    main()
