-- SMS Processing Pipeline Database Schema
-- Schema: customer_investigation_kb

-- Create schema if it doesn't exist
CREATE SCHEMA IF NOT EXISTS customer_investigation_kb;

-- Set search path to the schema
SET search_path TO customer_investigation_kb;

-- Table to store SMS messages
CREATE TABLE IF NOT EXISTS sms_messages (
    message_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    customer_id VARCHAR(255) NOT NULL,
    message_text TEXT NOT NULL,
    message_type VARCHAR(50) NOT NULL CHECK (message_type IN ('financial', 'non-financial')),
    s3_path VARCHAR(1000),
    processed BOOLEAN DEFAULT FALSE,
    analysis_result JSONB,
    classification JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    processed_at TIMESTAMP WITH TIME ZONE
);

-- Table to track batch processing
CREATE TABLE IF NOT EXISTS batch_processing (
    batch_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    customer_id VARCHAR(255) NOT NULL,
    message_ids UUID[] NOT NULL,
    batch_size INTEGER NOT NULL,
    status VARCHAR(50) DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    error_message TEXT
);

-- Table to track customer processing status
CREATE TABLE IF NOT EXISTS customer_processing_status (
    customer_id VARCHAR(255) PRIMARY KEY,
    total_messages INTEGER DEFAULT 0,
    processed_messages INTEGER DEFAULT 0,
    financial_messages INTEGER DEFAULT 0,
    non_financial_messages INTEGER DEFAULT 0,
    status VARCHAR(50) DEFAULT 'in_progress' CHECK (status IN ('in_progress', 'completed', 'failed')),
    started_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP WITH TIME ZONE,
    external_api_notified BOOLEAN DEFAULT FALSE,
    external_api_notified_at TIMESTAMP WITH TIME ZONE
);

-- Table to store processing logs
CREATE TABLE IF NOT EXISTS processing_logs (
    log_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    customer_id VARCHAR(255),
    batch_id UUID,
    message_id UUID,
    log_level VARCHAR(20) NOT NULL CHECK (log_level IN ('DEBUG', 'INFO', 'WARNING', 'ERROR')),
    message TEXT NOT NULL,
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Indexes for better performance
CREATE INDEX IF NOT EXISTS idx_sms_messages_customer_id ON sms_messages(customer_id);
CREATE INDEX IF NOT EXISTS idx_sms_messages_processed ON sms_messages(processed);
CREATE INDEX IF NOT EXISTS idx_sms_messages_customer_processed ON sms_messages(customer_id, processed);
CREATE INDEX IF NOT EXISTS idx_sms_messages_type ON sms_messages(message_type);
CREATE INDEX IF NOT EXISTS idx_sms_messages_created_at ON sms_messages(created_at);

CREATE INDEX IF NOT EXISTS idx_batch_processing_customer_id ON batch_processing(customer_id);
CREATE INDEX IF NOT EXISTS idx_batch_processing_status ON batch_processing(status);
CREATE INDEX IF NOT EXISTS idx_batch_processing_created_at ON batch_processing(created_at);

CREATE INDEX IF NOT EXISTS idx_customer_processing_status_status ON customer_processing_status(status);
CREATE INDEX IF NOT EXISTS idx_customer_processing_external_notified ON customer_processing_status(external_api_notified);

CREATE INDEX IF NOT EXISTS idx_processing_logs_customer_id ON processing_logs(customer_id);
CREATE INDEX IF NOT EXISTS idx_processing_logs_batch_id ON processing_logs(batch_id);
CREATE INDEX IF NOT EXISTS idx_processing_logs_level ON processing_logs(log_level);
CREATE INDEX IF NOT EXISTS idx_processing_logs_created_at ON processing_logs(created_at);

-- Function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger to automatically update updated_at
CREATE TRIGGER update_sms_messages_updated_at 
    BEFORE UPDATE ON sms_messages 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Function to update customer processing status
CREATE OR REPLACE FUNCTION update_customer_processing_status()
RETURNS TRIGGER AS $$
BEGIN
    -- Update processed count when a message is marked as processed
    IF NEW.processed = TRUE AND (OLD.processed IS NULL OR OLD.processed = FALSE) THEN
        UPDATE customer_processing_status 
        SET processed_messages = processed_messages + 1,
            updated_at = CURRENT_TIMESTAMP
        WHERE customer_id = NEW.customer_id;
        
        -- Check if all messages are processed
        UPDATE customer_processing_status 
        SET status = 'completed',
            completed_at = CURRENT_TIMESTAMP
        WHERE customer_id = NEW.customer_id 
        AND processed_messages >= total_messages
        AND status = 'in_progress';
    END IF;
    
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger to update customer status when messages are processed
CREATE TRIGGER update_customer_status_on_message_processed
    AFTER UPDATE ON sms_messages
    FOR EACH ROW
    EXECUTE FUNCTION update_customer_processing_status();

-- Function to initialize customer processing status
CREATE OR REPLACE FUNCTION initialize_customer_processing_status(
    p_customer_id VARCHAR(255),
    p_total_messages INTEGER,
    p_financial_count INTEGER,
    p_non_financial_count INTEGER
)
RETURNS VOID AS $$
BEGIN
    INSERT INTO customer_processing_status (
        customer_id, 
        total_messages, 
        financial_messages, 
        non_financial_messages,
        status
    ) VALUES (
        p_customer_id, 
        p_total_messages, 
        p_financial_count, 
        p_non_financial_count,
        'in_progress'
    )
    ON CONFLICT (customer_id) 
    DO UPDATE SET 
        total_messages = EXCLUDED.total_messages,
        financial_messages = EXCLUDED.financial_messages,
        non_financial_messages = EXCLUDED.non_financial_messages,
        status = 'in_progress',
        started_at = CURRENT_TIMESTAMP,
        processed_messages = 0,
        completed_at = NULL,
        external_api_notified = FALSE,
        external_api_notified_at = NULL;
END;
$$ language 'plpgsql';

-- Function to log processing events
CREATE OR REPLACE FUNCTION log_processing_event(
    p_customer_id VARCHAR(255),
    p_batch_id UUID DEFAULT NULL,
    p_message_id UUID DEFAULT NULL,
    p_log_level VARCHAR(20),
    p_message TEXT,
    p_metadata JSONB DEFAULT NULL
)
RETURNS VOID AS $$
BEGIN
    INSERT INTO processing_logs (
        customer_id,
        batch_id,
        message_id,
        log_level,
        message,
        metadata
    ) VALUES (
        p_customer_id,
        p_batch_id,
        p_message_id,
        p_log_level,
        p_message,
        p_metadata
    );
END;
$$ language 'plpgsql';

-- Grant permissions (adjust as needed for your setup)
-- GRANT USAGE ON SCHEMA customer_investigation_kb TO your_lambda_user;
-- GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA customer_investigation_kb TO your_lambda_user;
-- GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA customer_investigation_kb TO your_lambda_user;
