{"Accounts": {"entity_name": "The primary name of the institution managing the account, such as the bank ('SBI', 'HDFC Bank'), card issuer ('SBI Card'), or service provider ('Google Pay', 'YONO SBI').", "currency": "The currency of the payment. Default to 'INR' if unspecified.", "account_number": "The masked account identifier, which could be a bank account ('A/c X4884'), Provident Fund (PF) number, or other unique account ID.", "card_number": "The masked credit card number associated with the account status update, statement, or login alert. Example: 'ending 4465'.", "loan_id": "The unique identifier or account number for a loan if the message is a loan status update. Example: 'Loan A/C ****1772'.", "deposit_id": "The unique account or receipt number for a Fixed Deposit if the message is an FD status update.", "broker_name": "The name of the brokerage firm if the message is about a brokerage account. Example: 'ZERODHA'.", "account_status": "A short description of the account event, such as 'login alert', 'registration successful', 'e-Mandate cancelled', or 'KYC pending'.", "is_loan_delayed": "For loan accounts, set to true if the message indicates the account is 'overdue'.", "available_balance": "The current fund or account balance ONLY if explicitly stated in an account summary message. Do not confuse with transaction amounts.", "outstanding_balance": "The total outstanding amount for a loan or credit card account, typically found in statement or overdue alerts.", "total_due": "The total amount due specified in a credit card statement summary.", "minimum_due": "The minimum amount due specified in a credit card statement summary.", "due_date": "The payment due date specified in a credit card statement summary.", "otp": "A One-Time Password or numeric verification code if the message is for authentication or login."}, "Deposit & Withdrawal": {"transaction_amount": "The principal numeric value of the deposit or withdrawal. This is the amount of money being credited or debited in the transaction.", "currency": "The currency of the transaction amount (e.g., 'Rs', 'INR'). Default to 'INR' if unspecified.", "entity_name": "The name of the bank where the transaction occurred. If a salary credit, this can be the employer's name if mentioned.", "account_number": "The masked bank account number that was credited or debited.", "txn_ref": "The transaction reference number, if one is provided for the deposit or withdrawal.", "available_balance": "The final available balance in the account after the transaction, ONLY if explicitly mentioned in the message.", "card_number": "The masked debit card number used for an ATM withdrawal, if mentioned.", "employer_name": "The name of the company if the transaction is identified as a salary credit."}, "Investment": {"transaction_amount": "The numeric value of the investment, such as the amount invested in a mutual fund, the value of stocks traded, or the premium paid for insurance.", "currency": "The currency of the transaction amount. Default to 'INR' if unspecified.", "entity_name": "The name of the financial institution, asset management company (AMC), insurer, or stock exchange. Example: 'ZERODHA', 'NSE', 'LIC'.", "broker_name": "The name of the stockbroker through which the investment was made.", "company_name": "The specific name of the stock or security being traded if mentioned. Example: 'TATA POWER'.", "account_number": "The policy number for insurance, folio number for mutual funds, or other investment account identifier.", "interest_rate": "The interest rate associated with the investment, if mentioned.", "tenure_info": "The duration or term of the investment, such as for a Fixed Deposit (FD), if mentioned."}, "Money Transfer": {"transaction_amount": "The numeric amount of money being sent or received in the transfer.", "currency": "The currency of the transaction. Default to 'INR' if unspecified.", "entity_name": "The bank or wallet provider that is facilitating the transfer. Example: 'SBI', 'Paytm'.", "account_number": "The masked bank account number being credited or debited.", "vpa": "The UPI ID or Virtual Payment Address of the sender or receiver if explicitly mentioned in the message.", "upi_recipient": "The name of the person or entity to whom the funds were transferred ('trf to'). This is a key field for UPI transactions.", "txn_ref": "The unique reference number for the transaction. This can be a 'Refno', 'UTR', or 'IMPS' number.", "wallet_type": "The type of digital wallet involved, if specified. Example: 'Paytm Wallet'.", "broker_name": "The name of the brokerage firm if the transfer is for adding or withdrawing funds from a trading account."}, "Payment": {"transaction_amount": "The numeric amount that was paid for a bill, EMI, or credit card statement.", "currency": "The currency of the payment. Default to 'INR' if unspecified.", "entity_name": "The name of the entity to whom the payment was made, such as a credit card issuer ('SBI Card') or lender ('RBL Bank').", "card_number": "The masked credit card number associated with a credit card bill payment.", "loan_id": "The loan account number associated with an EMI payment.", "bill_id": "The unique identifier for a utility or service bill, such as a 'Consumer No.'.", "service_provider": "The name of the utility or service company for which a bill was paid. Example: 'Airtel', 'Jio'.", "is_cc_repayment": "Set to true if the message confirms a payment made towards a credit card bill.", "is_loan_repayment": "Set to true if the message confirms an EMI payment for a loan.", "is_loan_delayed": "Set to true if a loan payment message indicates that the amount is 'overdue'.", "due_date": "The due date for a bill, if specified in the payment confirmation.", "available_balance": "The updated available credit limit or account balance after a credit card payment, ONLY if explicitly mentioned."}, "Purchase": {"transaction_amount": "The numeric value of the purchase; the amount spent on goods or services.", "currency": "The currency of the purchase (e.g., 'Rs.', 'INR', 'USD'). Default to 'INR' if unspecified.", "entity_name": "The name of the merchant where the purchase was made. This is the most important entity for this type. Example: 'ZOMAT<PERSON>', 'MCDONALDS', 'AMAZON'.", "card_number": "The masked credit or debit card number used for the transaction.", "account_number": "The masked bank account number debited, typically in a UPI or debit card purchase.", "txn_ref": "The reference number ('Refno') associated with the purchase transaction.", "available_balance": "The final available balance in the bank account after the purchase, ONLY if explicitly stated in the message."}, "Non Financial": {"entity_name": "The name of the company, brand, or institution sending the promotional message, offer, or general notification. Example: 'JioSvc', 'Lenskart', 'CRED', 'Bajaj Finserv'."}}