"""
Test cases for SMS classifier
"""
import pytest
import sys
import os

# Add the layers/shared/python directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'layers', 'shared', 'python'))

from sms_classifier import SMSClassifier, clean_text

class TestSMSClassifier:
    """Test cases for SMS classifier"""
    
    def setup_method(self):
        """Setup test fixtures"""
        self.classifier = SMSClassifier()
    
    def test_clean_text(self):
        """Test text cleaning function"""
        # Test basic cleaning
        assert clean_text("  Hello World  ") == "hello world"
        
        # Test special character removal
        assert clean_text("Hello@#$%World") == "hello world"
        
        # Test empty string
        assert clean_text("") == ""
        assert clean_text(None) == ""
    
    def test_financial_message_classification(self):
        """Test classification of financial messages"""
        financial_messages = [
            "Your account has been debited by Rs 1000",
            "UPI transaction successful for Rs 500",
            "Credit card payment of Rs 2000 processed",
            "ATM withdrawal of Rs 3000 completed",
            "Bank balance is Rs 5000",
            "NEFT transfer of Rs 1500 received"
        ]
        
        for message in financial_messages:
            result = self.classifier.classify_message(message)
            assert result['message_type'] == 'financial', f"Failed for message: {message}"
            assert result['confidence'] > 0.5, f"Low confidence for financial message: {message}"
    
    def test_non_financial_message_classification(self):
        """Test classification of non-financial messages"""
        non_financial_messages = [
            "Happy birthday! Hope you have a great day",
            "Weather update: It will rain today",
            "Your order has been delivered",
            "Congratulations! You won a prize",
            "Meeting reminder for tomorrow at 3 PM",
            "Sale offer: 50% discount on all items"
        ]
        
        for message in non_financial_messages:
            result = self.classifier.classify_message(message)
            assert result['message_type'] == 'non-financial', f"Failed for message: {message}"
    
    def test_edge_cases(self):
        """Test edge cases"""
        # Empty message
        result = self.classifier.classify_message("")
        assert result['message_type'] == 'non-financial'
        assert result['confidence'] == 0.0
        
        # None message
        result = self.classifier.classify_message(None)
        assert result['message_type'] == 'non-financial'
        assert result['confidence'] == 0.0
        
        # Very short message
        result = self.classifier.classify_message("Hi")
        assert result['message_type'] == 'non-financial'
    
    def test_mixed_indicators(self):
        """Test messages with mixed financial and non-financial indicators"""
        # Message with both financial and promotional keywords
        message = "Congratulations! Your cashback of Rs 100 has been credited"
        result = self.classifier.classify_message(message)
        
        # Should be classified based on stronger indicators
        assert 'financial_score' in result
        assert 'non_financial_score' in result
        assert 'reasoning' in result
    
    def test_batch_classification(self):
        """Test batch classification"""
        messages = [
            "Your account debited by Rs 1000",
            "Happy birthday!",
            "UPI payment successful"
        ]
        
        results = self.classifier.batch_classify(messages)
        assert len(results) == 3
        
        # First and third should be financial, second should be non-financial
        assert results[0]['message_type'] == 'financial'
        assert results[1]['message_type'] == 'non-financial'
        assert results[2]['message_type'] == 'financial'
    
    def test_classification_result_structure(self):
        """Test that classification results have the expected structure"""
        message = "Your account debited by Rs 1000"
        result = self.classifier.classify_message(message)
        
        # Check required fields
        required_fields = [
            'message_type', 'confidence', 'financial_keywords',
            'financial_patterns', 'non_financial_keywords',
            'financial_score', 'non_financial_score', 'reasoning'
        ]
        
        for field in required_fields:
            assert field in result, f"Missing field: {field}"
        
        # Check data types
        assert isinstance(result['message_type'], str)
        assert isinstance(result['confidence'], (int, float))
        assert isinstance(result['financial_keywords'], list)
        assert isinstance(result['financial_patterns'], list)
        assert isinstance(result['non_financial_keywords'], list)
        assert isinstance(result['financial_score'], int)
        assert isinstance(result['non_financial_score'], int)
        assert isinstance(result['reasoning'], str)

if __name__ == "__main__":
    pytest.main([__file__])
