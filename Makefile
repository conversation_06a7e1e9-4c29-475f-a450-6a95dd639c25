.PHONY: build clean deploy validate test install-deps

# Python parameters
PYTHON_VERSION=3.11

# Lambda function directories
LAMBDA_DIRS := $(wildcard lambdas/*)
LAYER_DIRS := $(wildcard layers/*)

# Install dependencies for all Lambda functions and layers
install-deps:
	@echo "Installing dependencies for Lambda functions and layers..."
	@for dir in $(LAMBDA_DIRS); do \
		if [ -f "$$dir/requirements.txt" ]; then \
			echo "Installing dependencies for $$dir..."; \
			pip install -r "$$dir/requirements.txt" -t "$$dir/"; \
		fi; \
	done
	@for dir in $(LAYER_DIRS); do \
		if [ -f "$$dir/requirements.txt" ]; then \
			echo "Installing dependencies for $$dir..."; \
			mkdir -p "$$dir/python"; \
			pip install -r "$$dir/requirements.txt" -t "$$dir/python/"; \
		fi; \
	done
	@echo "Dependencies installed successfully!"

# Build all Lambda functions
build: clean install-deps
	@echo "Building Lambda functions and layers..."
	@echo "All Lambda functions and layers built successfully!"

# Clean build artifacts
clean:
	@echo "Cleaning build artifacts..."
	@find . -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true
	@find . -name "*.pyc" -delete 2>/dev/null || true
	@find . -name "*.pyo" -delete 2>/dev/null || true
	@find . -name ".pytest_cache" -type d -exec rm -rf {} + 2>/dev/null || true
	@echo "Clean completed!"

# Validate SAM template
validate:
	@echo "Validating SAM template..."
	sam validate --template template.yaml
	@echo "Template validation completed!"

# Build using SAM
sam-build: build
	@echo "Building with SAM..."
	sam build
	@echo "SAM build completed!"

# Deploy using SAM
deploy: sam-build
	@echo "Deploying with SAM..."
	sam deploy --guided
	@echo "Deployment completed!"

# Deploy with no prompts (for CI/CD)
deploy-ci: sam-build
	@echo "Deploying with SAM (CI mode)..."
	sam deploy --no-confirm-changeset --no-fail-on-empty-changeset
	@echo "Deployment completed!"

# Local development
local-start: sam-build
	@echo "Starting local API..."
	sam local start-api
	@echo "Local API started!"

# Run tests
test:
	@echo "Running tests..."
	python -m pytest tests/ -v
	@echo "Tests completed!"

# Format code
format:
	@echo "Formatting code..."
	black lambdas/ layers/ tests/ --line-length 100
	@echo "Code formatting completed!"

# Lint code
lint:
	@echo "Linting code..."
	flake8 lambdas/ layers/ tests/ --max-line-length=100
	@echo "Linting completed!"

# Create deployment package
package: build
	@echo "Creating deployment packages..."
	sam package --s3-bucket your-deployment-bucket --output-template-file packaged-template.yaml
	@echo "Packaging completed!"

# Initialize database schema
init-db:
	@echo "Initializing database schema..."
	psql -h $(DB_HOST) -U $(DB_USERNAME) -d $(DB_NAME) -f schema.sql
	@echo "Database schema initialized!"

# Create environment file template
create-env:
	@echo "Creating environment file template..."
	@echo "# SMS Processing Pipeline Environment Variables" > .env.example
	@echo "DB_HOST=modus-db-dev.cvui26wus1f1.ap-south-1.rds.amazonaws.com" >> .env.example
	@echo "DB_PORT=5432" >> .env.example
	@echo "DB_NAME=postgres" >> .env.example
	@echo "DB_USERNAME=postgres" >> .env.example
	@echo "DB_PASSWORD=your-database-password" >> .env.example
	@echo "DB_SCHEMA=customer_investigation_kb" >> .env.example
	@echo "BATCH_SIZE=50" >> .env.example
	@echo "OPENAI_API_KEY=your-openai-api-key" >> .env.example
	@echo "EXTERNAL_API_ENDPOINT=https://your-external-api.com/webhook" >> .env.example
	@echo "Environment file template created as .env.example"

# Help
help:
	@echo "Available targets:"
	@echo "  build          - Build all Lambda functions and layers"
	@echo "  clean          - Clean build artifacts"
	@echo "  install-deps   - Install Python dependencies"
	@echo "  validate       - Validate SAM template"
	@echo "  sam-build      - Build using SAM"
	@echo "  deploy         - Deploy using SAM (guided)"
	@echo "  deploy-ci      - Deploy using SAM (no prompts)"
	@echo "  local-start    - Start local API for development"
	@echo "  test           - Run tests"
	@echo "  format         - Format Python code"
	@echo "  lint           - Lint Python code"
	@echo "  package        - Create deployment packages"
	@echo "  init-db        - Initialize database schema"
	@echo "  create-env     - Create environment file template"
	@echo "  help           - Show this help message"
