<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="d89561d6-948c-4a6f-9096-6881800cc973" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/FINAL_PROCESSING_SUMMARY.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/HYBRID_SMS_PROCESSING_README.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/README.md" beforeDir="false" afterPath="$PROJECT_DIR$/README.md" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/SMS_PIPELINE_DEPLOYMENT_GUIDE.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/SMS_PIPELINE_README.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/SMS_PROCESSING_PIPELINE_SUMMARY.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/USAGE_GUIDE.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/compare_results.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/debug_files/debug_financial_filtering.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/demo_files/demo_financial_messages.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/demo_files/demo_financial_results.csv" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/demo_files/demo_hybrid_processing.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/demo_files/demo_hybrid_results.csv" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/generate_processing_summary.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/hybrid_sms_processed_results_complete.csv" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/lambdas/message-analysis/lambda_function.py" beforeDir="false" afterPath="$PROJECT_DIR$/lambdas/message-analysis/lambda_function.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lambdas/sms-segregation/lambda_function.py" beforeDir="false" afterPath="$PROJECT_DIR$/lambdas/sms-segregation/lambda_function.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/layers/shared/python/database.py" beforeDir="false" afterPath="$PROJECT_DIR$/layers/shared/python/database.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/layers/shared/python/sms_classifier.py" beforeDir="false" afterPath="$PROJECT_DIR$/layers/shared/python/sms_classifier.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/layers/shared/python/utils.py" beforeDir="false" afterPath="$PROJECT_DIR$/layers/shared/python/utils.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/process_full_sms_dataset.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/requirements.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/run_complete_hybrid_processing.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/run_sms_processing.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/sms-pipeline.gitignore" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/sms_backup.csv" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/sms_backup_processor.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/sms_parser.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/sms_processed_results.csv" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/sms_processed_results_summary.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/test_files/comprehensive_csv_tests.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/test_files/comprehensive_sms_data_test.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/test_files/csv_test_generator.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/test_files/final_test_suite.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/test_files/test_100_percent_final.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/test_files/test_both_csv_files.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/test_files/test_full_processing.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/test_files/test_hybrid_openai_results.csv" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/test_files/test_hybrid_processor.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/test_files/test_hybrid_results.csv" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/test_files/test_hybrid_subset_results.csv" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/test_files/test_hybrid_with_openai.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/test_files/test_specific_fixes.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/test_files/validate_all_fixes.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/test_pipeline.py" beforeDir="false" afterPath="$PROJECT_DIR$/test_pipeline.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unnecessary_files/ADDITIONAL_WRONG_MAPPING_FIXES_SUMMARY.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unnecessary_files/CLASSIFICATION_FIX_SUMMARY.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unnecessary_files/FINANCIAL_OTP_EMANDATES_FIX_SUMMARY.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unnecessary_files/OTP_PROMOTIONAL_CLASSIFICATION_SUMMARY.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unnecessary_files/SMS-Data.csv" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unnecessary_files/SMS_PROCESSING_README.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unnecessary_files/WRONG_MAPPING_FIXES_SUMMARY.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unnecessary_files/demo_complete_workflow.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unnecessary_files/demo_results.csv" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unnecessary_files/demo_results_summary.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unnecessary_files/demo_sms_backup.csv" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unnecessary_files/fix_existing_classifications.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unnecessary_files/fix_financial_otp_emandates.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unnecessary_files/fix_promotional_credit_classification.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unnecessary_files/fix_wrong_mapping_classifications.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unnecessary_files/sample_results.csv" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unnecessary_files/sample_results_summary.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unnecessary_files/small_sample.csv" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unnecessary_files/sms_processed_results.csv" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unnecessary_files/sms_processed_results_corrected.csv" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unnecessary_files/sms_processed_results_final.csv" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unnecessary_files/sms_processed_results_final_corrected.csv" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unnecessary_files/sms_processed_results_fixed.csv" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unnecessary_files/sms_processed_results_summary.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unnecessary_files/test_classification_fix.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unnecessary_files/test_financial_otp_fix.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unnecessary_files/test_otp_promotional_classification.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unnecessary_files/test_results.csv" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unnecessary_files/test_results_summary.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unnecessary_files/test_sms_processing.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unnecessary_files/test_specific_wrong_messages.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unnecessary_files/test_wrong_mapping_fixes.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unnecessary_files/wrong_mapping.csv" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/utils.py" beforeDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProjectColorInfo"><![CDATA[{
  "associatedIndex": 2
}]]></component>
  <component name="ProjectId" id="30dp7AymVHA8uGoZas0JoRWhDlH" />
  <component name="ProjectViewState">
    <option name="autoscrollToSource" value="true" />
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="openDirectoriesWithSingleClick" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "git-widget-placeholder": "dev",
    "last_opened_file_path": "/Users/<USER>/Documents/modus/sms_parser_async",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-f27c65a3e318-JavaScript-PY-251.23774.444" />
        <option value="bundled-python-sdk-890ed5b35930-d9c5bdb153f4-com.jetbrains.pycharm.pro.sharedIndexes.bundled-PY-251.23774.444" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="d89561d6-948c-4a6f-9096-6881800cc973" name="Changes" comment="" />
      <created>1753967135204</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1753967135204</updated>
      <workItem from="1753967136669" duration="745000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>